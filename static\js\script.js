document.addEventListener('DOMContentLoaded', function() {
    // 获取并显示局域网访问地址
    function fetchNetworkInfo() {
        fetch('/network-info')
            .then(response => response.json())
            .then(data => {
                const networkAddressElement = document.getElementById('network-address');
                if (networkAddressElement) {
                    networkAddressElement.textContent = `http://${data.local_ip}:${data.port}`;
                }
            })
            .catch(error => {
                console.error('获取网络信息失败:', error);
                const networkAddressElement = document.getElementById('network-address');
                if (networkAddressElement) {
                    networkAddressElement.textContent = '无法获取局域网地址';
                }
            });
    }

    // 页面加载时获取网络信息
    fetchNetworkInfo();

    // 清理临时文件按钮
    const cleanupBtn = document.getElementById('cleanup-btn');
    if (cleanupBtn) {
        cleanupBtn.addEventListener('click', function() {
            if (confirm('确定要清理所有临时文件吗？这将删除所有上传和处理的文件。')) {
                cleanupBtn.disabled = true;
                cleanupBtn.textContent = '清理中...';

                fetch('/cleanup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({}),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('临时文件清理成功！');
                        // 刷新文件列表
                        uploadedFiles = [];
                        updateFileList();
                        updateButtonState();
                    } else {
                        alert('清理失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('清理请求失败:', error);
                    alert('清理请求失败，请查看控制台了解详情。');
                })
                .finally(() => {
                    cleanupBtn.disabled = false;
                    cleanupBtn.textContent = '清理临时文件';
                });
            }
        });
    }

    // 初始化时禁用处理按钮
    function updateButtonState() {
        const hasFiles = uploadedFiles.length > 0;
        const actionButtons = document.querySelectorAll('.action-btn');

        actionButtons.forEach(btn => {
            if (hasFiles) {
                btn.classList.remove('disabled');
                btn.disabled = false;
            } else {
                btn.classList.add('disabled');
                btn.disabled = true;
            }
        });
    }
    // DOM Elements
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('file-input');
    const uploadBtn = document.getElementById('upload-btn');
    const fileListContainer = document.getElementById('file-list-container');
    const fileList = document.getElementById('file-list');
    const processingOptions = document.getElementById('processing-options');
    const previewBtn = document.getElementById('preview-btn');
    const removeHeaderBtn = document.getElementById('remove-header-btn');
    const forceProcessBtn = document.getElementById('force-process-btn');
    const removeBackgroundBtn = document.getElementById('remove-background-btn');
    const convertToWordBtn = document.getElementById('convert-to-word-btn');
    const previewContainer = document.getElementById('preview-container');
    const previewFileType = document.getElementById('preview-file-type');
    const previewDetectionInfo = document.getElementById('preview-detection-info');
    const previewPages = document.getElementById('preview-pages');
    const previewProcessBtn = document.getElementById('preview-process-btn');
    const previewCancelBtn = document.getElementById('preview-cancel-btn');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const resultContainer = document.getElementById('result-container');
    const resultMessage = document.getElementById('result-message');

    // 存储下载信息的变量
    let downloadFilePath = '';
    let downloadFileName = '';

    // Store uploaded files
    let uploadedFiles = [];

    // 初始化时禁用处理按钮
    updateButtonState();

    // Event Listeners
    uploadBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', handleFileUpload);
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    previewBtn.addEventListener('click', previewSelectedFile);
    removeHeaderBtn.addEventListener('click', () => processFiles('remove_header'));
    forceProcessBtn.addEventListener('click', () => processFiles('force_process'));
    removeBackgroundBtn.addEventListener('click', () => processFiles('remove_background'));
    convertToWordBtn.addEventListener('click', () => processFiles('convert_to_word'));
    previewProcessBtn.addEventListener('click', () => {
        previewContainer.style.display = 'none';
        processFiles('remove_header'); // 这个操作会自动下载文件
    });
    previewCancelBtn.addEventListener('click', () => {
        previewContainer.style.display = 'none';
    });

    // Functions
    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('drag-over');

        const files = e.dataTransfer.files;
        handleFiles(files);
    }

    function handleFileUpload(e) {
        const files = e.target.files;
        handleFiles(files);
    }

    function handleFiles(files) {
        const validFiles = Array.from(files).filter(file => file.type === 'application/pdf');

        if (validFiles.length === 0) {
            alert('请上传PDF文件');
            return;
        }

        // Create FormData and upload files
        const formData = new FormData();
        validFiles.forEach(file => {
            formData.append('files[]', file);
        });

        // Show loading state
        uploadArea.innerHTML = '<i class="fas fa-spinner fa-spin"></i><p>上传中...</p>';

        // Upload files to server
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
                resetUploadArea();
                return;
            }

            // Store uploaded files
            uploadedFiles = data.files;

            // Display file list
            displayFileList();

            // Reset upload area
            resetUploadArea();

            // 更新按钮状态
            updateButtonState();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('上传失败，请重试');
            resetUploadArea();
        });
    }

    function resetUploadArea() {
        uploadArea.innerHTML = `
            <i class="fas fa-file-pdf"></i>
            <p>拖放PDF文件到这里或点击上传</p>
            <button id="upload-btn" class="btn">选择文件</button>
        `;
        document.getElementById('upload-btn').addEventListener('click', () => fileInput.click());
    }

    function displayFileList() {
        fileList.innerHTML = '';

        uploadedFiles.forEach((file, index) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <i class="fas fa-file-pdf"></i>
                <span class="file-name">${file.original_name}</span>
                <i class="fas fa-times remove-file" data-index="${index}"></i>
            `;
            fileList.appendChild(li);
        });

        // Add event listeners to remove buttons
        document.querySelectorAll('.remove-file').forEach(btn => {
            btn.addEventListener('click', function() {
                const index = parseInt(this.getAttribute('data-index'));
                uploadedFiles.splice(index, 1);

                if (uploadedFiles.length === 0) {
                    fileListContainer.style.display = 'none';
                } else {
                    displayFileList();
                }

                // 更新按钮状态
                updateButtonState();
            });
        });

        fileListContainer.style.display = 'block';
    }

    function processFiles(action) {
        if (uploadedFiles.length === 0) {
            alert('请先上传文件');
            return;
        }

        // Show progress
        progressContainer.style.display = 'block';

        // Animate progress bar
        let progress = 0;
        const interval = setInterval(() => {
            progress += 1;
            progressBar.style.width = `${Math.min(progress, 95)}%`;

            if (progress >= 95) {
                clearInterval(interval);
            }
        }, 100);

        // Send processing request
        fetch('/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                files: uploadedFiles,
                action: action
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            clearInterval(interval);
            progressBar.style.width = '100%';

            if (data.error) {
                progressText.textContent = `处理失败: ${data.error}`;
                // Add a retry button
                const retryBtn = document.createElement('button');
                retryBtn.className = 'btn';
                retryBtn.textContent = '重试';
                retryBtn.style.marginTop = '10px';
                retryBtn.addEventListener('click', () => {
                    progressContainer.style.display = 'none';
                });
                progressContainer.appendChild(retryBtn);
                return;
            }

            // Hide progress and automatically download the file
            setTimeout(() => {
                progressContainer.style.display = 'none';

                // Store download info for automatic download
                downloadFilePath = data.file_path;
                downloadFileName = data.file_name;

                // Automatically download the file
                if (action === 'remove_header' || action === 'force_process' ||
                    action === 'remove_background' || action === 'convert_to_word') {
                    // Start download immediately
                    downloadProcessedFile(true);
                }
            }, 500);
        })
        .catch(error => {
            clearInterval(interval);
            console.error('Error:', error);
            progressText.textContent = `处理失败: ${error.message || '未知错误，请重试'}`;

            // Add a retry button
            const retryBtn = document.createElement('button');
            retryBtn.className = 'btn';
            retryBtn.textContent = '重试';
            retryBtn.style.marginTop = '10px';
            retryBtn.addEventListener('click', () => {
                progressContainer.style.display = 'none';
            });
            progressContainer.appendChild(retryBtn);
        });
    }

    function previewSelectedFile() {
        if (uploadedFiles.length === 0) {
            alert('请先上传文件');
            return;
        }

        // If multiple files, use the first one for preview
        const fileToPreview = uploadedFiles[0];

        // Show progress
        progressContainer.style.display = 'block';
        progressText.textContent = '正在生成预览...';

        // Animate progress bar
        let progress = 0;
        const interval = setInterval(() => {
            progress += 1;
            progressBar.style.width = `${Math.min(progress, 95)}%`;

            if (progress >= 95) {
                clearInterval(interval);
            }
        }, 50);

        // Send preview request
        fetch('/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file: fileToPreview
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            clearInterval(interval);
            progressBar.style.width = '100%';

            if (data.error) {
                progressText.textContent = `预览失败: ${data.error}`;
                // Add a retry button
                const retryBtn = document.createElement('button');
                retryBtn.className = 'btn';
                retryBtn.textContent = '重试';
                retryBtn.style.marginTop = '10px';
                retryBtn.addEventListener('click', () => {
                    progressContainer.style.display = 'none';
                });
                progressContainer.appendChild(retryBtn);
                return;
            }

            // Hide progress and show preview
            setTimeout(() => {
                progressContainer.style.display = 'none';
                displayPreview(data);
                previewContainer.style.display = 'block';
            }, 500);
        })
        .catch(error => {
            clearInterval(interval);
            console.error('Error:', error);
            progressText.textContent = `预览失败: ${error.message || '未知错误，请重试'}`;

            // Add a retry button
            const retryBtn = document.createElement('button');
            retryBtn.className = 'btn';
            retryBtn.textContent = '重试';
            retryBtn.style.marginTop = '10px';
            retryBtn.addEventListener('click', () => {
                progressContainer.style.display = 'none';
            });
            progressContainer.appendChild(retryBtn);
        });
    }

    function displayPreview(previewData) {
        // Clear previous preview
        previewPages.innerHTML = '';

        // Set file type
        previewFileType.textContent = `文件类型: ${previewData.type === 'text' ? '文本型PDF' : '扫描型PDF'}`;

        // Set detection info
        let detectionInfo = '';
        if (previewData.type === 'text') {
            const headerCount = previewData.headers.length;
            const stampCount = previewData.stamps.length;
            detectionInfo = `检测到 ${headerCount} 个红头区域, ${stampCount} 个公章`;
        } else {
            const redPagesCount = previewData.red_areas.length;
            detectionInfo = `检测到 ${redPagesCount} 页包含红色内容`;
        }
        previewDetectionInfo.textContent = detectionInfo;

        // Display page thumbnails
        previewData.pages.forEach(page => {
            const pageDiv = document.createElement('div');
            pageDiv.className = 'preview-page';

            // Add thumbnail
            const img = document.createElement('img');
            img.src = `data:image/jpeg;base64,${page.thumbnail}`;
            pageDiv.appendChild(img);

            // Add page number
            const pageNumber = document.createElement('div');
            pageNumber.className = 'page-number';
            pageNumber.textContent = `第 ${page.page} 页`;
            pageDiv.appendChild(pageNumber);

            // Add detections
            if (previewData.type === 'text') {
                // Add headers
                previewData.headers.forEach(header => {
                    if (header.page === page.page) {
                        const detection = document.createElement('div');
                        detection.className = 'detection';
                        detection.style.top = `${(header.y / page.height) * 100}%`;
                        detection.style.left = `${(header.x / page.width) * 100}%`;
                        detection.style.width = `${(header.width / page.width) * 100}%`;
                        detection.style.height = `${(header.height / page.height) * 100}%`;
                        pageDiv.appendChild(detection);
                    }
                });

                // Add stamps
                previewData.stamps.forEach(stamp => {
                    if (stamp.page === page.page) {
                        const detection = document.createElement('div');
                        detection.className = 'detection';
                        detection.style.top = `${(stamp.y / page.height) * 100}%`;
                        detection.style.left = `${(stamp.x / page.width) * 100}%`;
                        detection.style.width = `${(stamp.width / page.width) * 100}%`;
                        detection.style.height = `${(stamp.height / page.height) * 100}%`;
                        detection.style.borderRadius = '50%';
                        pageDiv.appendChild(detection);
                    }
                });
            } else {
                // For scanned PDFs, highlight pages with red content
                if (previewData.red_areas.some(area => area.page === page.page)) {
                    pageDiv.style.border = '2px solid red';
                }
            }

            previewPages.appendChild(pageDiv);
        });
    }

    function downloadProcessedFile(autoDownload = false) {
        // 使用全局变量获取下载信息
        const filePath = downloadFilePath;
        const fileName = downloadFileName;

        if (!filePath) {
            alert('下载失败，文件路径不存在');
            return;
        }

        // Create a download link
        const a = document.createElement('a');
        a.href = `/download/${filePath}`;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        console.log(`正在下载文件: ${fileName}`); // 添加日志，帮助调试

        // 自动下载后不清除文件列表，允许用户继续处理同一批文件
        // 不再需要显示结果容器
    }
});

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的扫描型PDF处理功能
"""

import os
import sys
import logging
import comprehensive_processor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_scanned_pdf_processing():
    """测试扫描型PDF处理功能"""
    print("="*60)
    print("测试扫描型PDF红头和公章去除功能")
    print("="*60)
    
    # 测试文件路径（需要根据实际情况调整）
    test_files = [
        "test_scanned.pdf",  # 扫描型PDF测试文件
        "test_text.pdf"      # 文本型PDF测试文件
    ]
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"测试文件不存在: {test_file}")
            continue
            
        print(f"\n处理文件: {test_file}")
        
        # 检测PDF类型
        is_scanned = comprehensive_processor.is_scanned_pdf(test_file)
        print(f"PDF类型: {'扫描型' if is_scanned else '文本型'}")
        
        # 生成输出文件名
        output_file = f"processed_{test_file}"
        
        try:
            # 使用改进的处理方法
            if is_scanned:
                print("使用改进的扫描型PDF处理方法...")
                result = comprehensive_processor.process_scanned_pdf_remove_red(test_file, output_file)
            else:
                print("使用全面处理方法...")
                result = comprehensive_processor.process_pdf_comprehensive(test_file, output_file, "remove_red")
            
            if result.get("success"):
                print(f"✓ 处理成功: {output_file}")
            else:
                print(f"✗ 处理失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"✗ 处理异常: {str(e)}")
            import traceback
            traceback.print_exc()

def test_fill_seal_region():
    """测试公章区域填充功能"""
    print("\n" + "="*60)
    print("测试公章区域填充功能")
    print("="*60)
    
    try:
        import cv2
        import numpy as np
        
        # 创建一个测试图像（模拟公章区域）
        test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加红色圆形（模拟公章）
        cv2.circle(test_image, (50, 50), 40, (0, 0, 255), -1)  # 红色圆形
        
        # 添加黑色文字（模拟公章内文字）
        cv2.putText(test_image, "TEST", (30, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
        
        print("创建测试图像...")
        
        # 测试填充功能
        result = comprehensive_processor.fill_seal_region(test_image, 10, 10, 80, 80)
        
        if result:
            print("✓ 公章区域填充测试成功")
        else:
            print("✗ 公章区域填充测试失败")
            
    except Exception as e:
        print(f"✗ 公章区域填充测试异常: {str(e)}")

def test_stamp_processing():
    """测试公章处理功能"""
    print("\n" + "="*60)
    print("测试公章处理功能")
    print("="*60)
    
    try:
        import cv2
        import numpy as np
        
        # 创建一个测试图像
        test_image = np.ones((200, 200, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加红色圆形（模拟公章）
        cv2.circle(test_image, (100, 100), 50, (0, 0, 255), -1)  # 红色圆形
        
        # 添加黑色文字（模拟公章内文字）
        cv2.putText(test_image, "SEAL", (75, 105), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        print("创建测试图像...")
        
        # 测试公章处理功能
        processed_image = comprehensive_processor.process_stamps_in_scanned_image(test_image)
        
        if processed_image is not None:
            print("✓ 公章处理测试成功")
            
            # 检查是否有变化
            if not np.array_equal(test_image, processed_image):
                print("✓ 图像已被处理（有变化）")
            else:
                print("! 图像未发生变化")
        else:
            print("✗ 公章处理测试失败")
            
    except Exception as e:
        print(f"✗ 公章处理测试异常: {str(e)}")

if __name__ == "__main__":
    print("开始测试改进后的PDF处理功能...")
    
    # 测试各个功能模块
    test_fill_seal_region()
    test_stamp_processing()
    test_scanned_pdf_processing()
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)

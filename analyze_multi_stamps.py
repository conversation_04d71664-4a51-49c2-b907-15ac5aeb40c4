#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析多公章处理问题
"""

import os
import cv2
import numpy as np
from PIL import Image
import fitz
import logging
from pdf2image import convert_from_path
import comprehensive_processor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def analyze_multi_stamp_pdf(pdf_path):
    """分析包含多个公章的PDF文件"""
    print(f"\n{'='*80}")
    print(f"分析文件: {pdf_path}")
    print(f"{'='*80}")
    
    try:
        # 检测PDF类型
        is_scanned = comprehensive_processor.is_scanned_pdf(pdf_path)
        print(f"PDF类型: {'扫描型' if is_scanned else '文本型'}")
        
        # 将PDF转换为图像
        images = convert_from_path(pdf_path, dpi=300)
        print(f"总页数: {len(images)}")
        
        for i, img in enumerate(images):
            if i >= 3:  # 只分析前3页
                break
                
            print(f"\n{'-'*60}")
            print(f"页面 {i+1}")
            print(f"{'-'*60}")
            
            # 转换为OpenCV格式
            opencv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            height, width = opencv_img.shape[:2]
            print(f"图像尺寸: {width}x{height}")
            
            # 分析红色区域
            analyze_red_regions(opencv_img, i+1)
            
            # 分析公章
            analyze_stamps_in_page(opencv_img, i+1)
            
    except Exception as e:
        print(f"分析文件时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_red_regions(image, page_num):
    """分析页面中的红色区域"""
    print(f"\n--- 红色区域分析 ---")
    
    # 转换为HSV色彩空间
    img_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 使用多种红色检测参数
    red_detection_params = [
        # 高饱和度
        ([0, 150, 150], [10, 255, 255], [170, 150, 150], [180, 255, 255], "高饱和度"),
        # 中等饱和度
        ([0, 80, 80], [15, 255, 255], [165, 80, 80], [180, 255, 255], "中等饱和度"),
        # 低饱和度
        ([0, 40, 40], [20, 255, 255], [160, 40, 40], [180, 255, 255], "低饱和度"),
        # 极低饱和度
        ([0, 20, 20], [25, 255, 255], [155, 20, 20], [180, 255, 255], "极低饱和度"),
    ]
    
    total_pixels = image.shape[0] * image.shape[1]
    
    for i, (lower1, upper1, lower2, upper2, name) in enumerate(red_detection_params):
        lower_red1 = np.array(lower1)
        upper_red1 = np.array(upper1)
        lower_red2 = np.array(lower2)
        upper_red2 = np.array(upper2)
        
        mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
        red_mask = mask1 + mask2
        
        red_pixels = cv2.countNonZero(red_mask)
        red_ratio = red_pixels / total_pixels * 100
        
        print(f"{name}: {red_pixels} 像素 ({red_ratio:.3f}%)")

def analyze_stamps_in_page(image, page_num):
    """分析页面中的公章"""
    print(f"\n--- 公章分析 ---")
    
    # 转换为HSV色彩空间
    img_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 使用多种红色检测参数
    red_masks = []
    
    # 参数组1：高饱和度红色
    lower_red1_high = np.array([0, 150, 150])
    upper_red1_high = np.array([10, 255, 255])
    lower_red2_high = np.array([170, 150, 150])
    upper_red2_high = np.array([180, 255, 255])
    mask1_high = cv2.inRange(img_hsv, lower_red1_high, upper_red1_high)
    mask2_high = cv2.inRange(img_hsv, lower_red2_high, upper_red2_high)
    red_masks.append(mask1_high + mask2_high)
    
    # 参数组2：中等饱和度红色
    lower_red1_med = np.array([0, 80, 80])
    upper_red1_med = np.array([15, 255, 255])
    lower_red2_med = np.array([165, 80, 80])
    upper_red2_med = np.array([180, 255, 255])
    mask1_med = cv2.inRange(img_hsv, lower_red1_med, upper_red1_med)
    mask2_med = cv2.inRange(img_hsv, lower_red2_med, upper_red2_med)
    red_masks.append(mask1_med + mask2_med)
    
    # 参数组3：低饱和度红色
    lower_red1_low = np.array([0, 40, 40])
    upper_red1_low = np.array([20, 255, 255])
    lower_red2_low = np.array([160, 40, 40])
    upper_red2_low = np.array([180, 255, 255])
    mask1_low = cv2.inRange(img_hsv, lower_red1_low, upper_red1_low)
    mask2_low = cv2.inRange(img_hsv, lower_red2_low, upper_red2_low)
    red_masks.append(mask1_low + mask2_low)
    
    # 参数组4：极低饱和度红色
    lower_red1_vlow = np.array([0, 20, 20])
    upper_red1_vlow = np.array([25, 255, 255])
    lower_red2_vlow = np.array([155, 20, 20])
    upper_red2_vlow = np.array([180, 255, 255])
    mask1_vlow = cv2.inRange(img_hsv, lower_red1_vlow, upper_red1_vlow)
    mask2_vlow = cv2.inRange(img_hsv, lower_red2_vlow, upper_red2_vlow)
    red_masks.append(mask1_vlow + mask2_vlow)
    
    # 合并所有红色检测结果
    combined_red_mask = np.zeros_like(red_masks[0])
    for mask in red_masks:
        combined_red_mask = cv2.bitwise_or(combined_red_mask, mask)
    
    # 查找红色区域的轮廓
    contours, _ = cv2.findContours(combined_red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"检测到 {len(contours)} 个红色轮廓")
    
    # 分析每个轮廓
    stamp_candidates = []
    red_header_candidates = []
    
    for j, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area < 100:  # 忽略太小的区域
            continue
        
        # 计算轮廓特征
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
        
        circularity = 4 * np.pi * area / (perimeter * perimeter)
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = float(w) / h if h > 0 else 0
        
        # 计算位置特征
        center_y = y + h // 2
        relative_y = center_y / image.shape[0]  # 相对于页面高度的位置
        
        print(f"轮廓{j+1}: 面积={area:.0f}, 圆形度={circularity:.3f}, 宽高比={aspect_ratio:.3f}")
        print(f"        位置=({x},{y}), 尺寸={w}x{h}, 相对位置={relative_y:.3f}")
        
        # 判断是红头还是公章
        is_top_region = relative_y < 0.3  # 页面顶部30%
        is_large_width = w > image.shape[1] * 0.3  # 宽度超过页面30%
        
        # 公章判断条件
        is_stamp = False
        stamp_type = ""
        
        # 条件1：标准圆形公章
        if circularity > 0.4 and 0.7 <= aspect_ratio <= 1.3 and 1000 <= area <= 20000:
            is_stamp = True
            stamp_type = "标准圆形"
            
        # 条件2：椭圆形公章
        elif circularity > 0.3 and 0.6 <= aspect_ratio <= 1.4 and 500 <= area <= 25000:
            is_stamp = True
            stamp_type = "椭圆形"
            
        # 条件3：不规则但紧凑的公章
        elif circularity > 0.2 and 0.5 <= aspect_ratio <= 1.5 and 800 <= area <= 30000:
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0
            if solidity > 0.5:
                is_stamp = True
                stamp_type = "不规则紧凑"
                
        # 条件4：特殊情况公章
        elif circularity > 0.5 and 0.6 <= aspect_ratio <= 0.8 and 3000 <= area <= 5000:
            is_stamp = True
            stamp_type = "特殊椭圆"
            
        # 条件5：大型公章
        elif circularity > 0.7 and 0.9 <= aspect_ratio <= 1.1 and 100000 <= area <= 300000:
            is_stamp = True
            stamp_type = "大型圆形"
        
        # 条件6：中型公章（新增）
        elif circularity > 0.3 and 0.7 <= aspect_ratio <= 1.3 and 20000 <= area <= 100000:
            is_stamp = True
            stamp_type = "中型公章"
        
        if is_stamp:
            stamp_candidates.append({
                'contour': contour,
                'area': area,
                'circularity': circularity,
                'aspect_ratio': aspect_ratio,
                'bbox': (x, y, w, h),
                'type': stamp_type,
                'relative_y': relative_y
            })
            print(f"        ✓ 识别为公章 ({stamp_type})")
        elif is_top_region and is_large_width:
            red_header_candidates.append({
                'contour': contour,
                'area': area,
                'bbox': (x, y, w, h),
                'relative_y': relative_y
            })
            print(f"        ✓ 识别为红头文字")
        else:
            print(f"        ✗ 未识别 (圆形度={circularity:.3f}, 宽高比={aspect_ratio:.3f}, 位置={relative_y:.3f})")
    
    print(f"\n总结:")
    print(f"  公章候选: {len(stamp_candidates)} 个")
    print(f"  红头候选: {len(red_header_candidates)} 个")
    
    # 详细显示公章信息
    if stamp_candidates:
        print(f"\n公章详情:")
        for i, stamp in enumerate(stamp_candidates):
            x, y, w, h = stamp['bbox']
            print(f"  公章{i+1} ({stamp['type']}): 位置({x},{y}), 尺寸{w}x{h}, 面积{stamp['area']:.0f}")

def compare_processing_results():
    """比较处理前后的结果"""
    print(f"\n{'='*80}")
    print("比较处理前后的结果")
    print(f"{'='*80}")
    
    test_files = [
        ("sl/4.pdf", "sl/去红头-4.pdf"),
    ]
    
    for original, processed in test_files:
        if os.path.exists(original) and os.path.exists(processed):
            print(f"\n比较: {original} -> {processed}")
            
            # 分析原始文件
            print("原始文件:")
            analyze_multi_stamp_pdf(original)
            
            # 分析处理后文件
            print("\n处理后文件:")
            analyze_multi_stamp_pdf(processed)
            
            # 统计处理效果
            print(f"\n处理效果统计:")
            compare_red_pixels(original, processed)
        else:
            print(f"文件不存在: {original} 或 {processed}")

def compare_red_pixels(original_file, processed_file):
    """比较处理前后的红色像素"""
    try:
        def count_red_pixels(image):
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 50, 50])
            upper_red2 = np.array([180, 255, 255])
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = mask1 + mask2
            return cv2.countNonZero(red_mask)
        
        # 转换为图像进行比较
        original_images = convert_from_path(original_file, dpi=150)
        processed_images = convert_from_path(processed_file, dpi=150)
        
        for i, (orig_img, proc_img) in enumerate(zip(original_images[:3], processed_images[:3])):
            orig_cv = cv2.cvtColor(np.array(orig_img), cv2.COLOR_RGB2BGR)
            proc_cv = cv2.cvtColor(np.array(proc_img), cv2.COLOR_RGB2BGR)
            
            orig_red = count_red_pixels(orig_cv)
            proc_red = count_red_pixels(proc_cv)
            
            total_pixels = orig_cv.shape[0] * orig_cv.shape[1]
            orig_red_ratio = orig_red / total_pixels * 100
            proc_red_ratio = proc_red / total_pixels * 100
            
            reduction = (orig_red - proc_red) / orig_red * 100 if orig_red > 0 else 0
            
            print(f"页面{i+1}:")
            print(f"  原始红色像素: {orig_red} ({orig_red_ratio:.2f}%)")
            print(f"  处理后红色像素: {proc_red} ({proc_red_ratio:.2f}%)")
            print(f"  红色像素减少: {reduction:.1f}%")
            
            if reduction > 90:
                print(f"  ✓ 处理效果优秀")
            elif reduction > 70:
                print(f"  ⚠ 处理效果良好")
            elif reduction > 50:
                print(f"  ⚠ 处理效果一般")
            else:
                print(f"  ✗ 处理效果不佳")
                
    except Exception as e:
        print(f"比较红色像素时出错: {str(e)}")

if __name__ == "__main__":
    print("开始分析多公章处理问题...")
    
    # 分析原始文件
    if os.path.exists("sl/4.pdf"):
        analyze_multi_stamp_pdf("sl/4.pdf")
    
    # 比较处理结果
    compare_processing_results()
    
    print(f"\n{'='*80}")
    print("分析完成")
    print(f"{'='*80}")

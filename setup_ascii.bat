@echo off
echo Setting up PDF Editor environment...

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed! Please install Python 3.8 or higher.
    echo You can download Python from https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Check if pip is installed
python -m pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo pip is not installed! Please install pip first.
    pause
    exit /b 1
)

REM Check if virtualenv is installed
python -m pip show virtualenv > nul 2>&1
if %errorlevel% neq 0 (
    echo Installing virtualenv...
    python -m pip install virtualenv
)

REM Create virtual environment
if not exist venv (
    echo Creating virtual environment...
    python -m virtualenv venv
) else (
    echo Virtual environment already exists, skipping creation.
)

REM Activate virtual environment and install dependencies
echo Installing dependencies...
call venv\Scripts\activate.bat
python -m pip install -r requirements.txt

REM Create necessary directories
echo Creating necessary directories...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

REM Download and install Poppler manually
echo.
echo =====================================================================
echo Please download and install Poppler manually:
echo 1. Download Poppler from: 
echo    https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
echo 2. Extract the downloaded file
echo 3. Rename the extracted folder to "poppler"
echo 4. Place the "poppler" folder in the current directory
echo =====================================================================
echo.

set /p answer=Have you completed the Poppler installation? (y/n): 
if /i "%answer%" neq "y" (
    echo Please complete the Poppler installation before running the application.
    pause
    exit /b 1
)

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo Poppler installation not detected correctly. Please ensure:
    echo 1. You have downloaded and extracted Poppler
    echo 2. The extracted folder is renamed to "poppler"
    echo 3. The "poppler" folder is placed in the current directory
    echo 4. The folder structure is correct (should include Library\bin subdirectory)
    pause
    exit /b 1
)

echo.
echo Setup complete!
echo Use run_ascii.bat to start the application.
echo.
pause

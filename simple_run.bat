@echo off
echo Starting PDF Editor...

REM Check if virtual environment exists
if not exist venv (
    echo Virtual environment does not exist! Please run install_wheels.bat first.
    pause
    exit /b 1
)

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo Poppler library does not exist or is incomplete!
    echo Please download and install Poppler:
    echo 1. Download from: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
    echo 2. Extract the ZIP file
    echo 3. Rename the folder to "poppler"
    echo 4. Place it in the current directory
    pause
    exit /b 1
)

REM Set Poppler environment variable
set PATH=%PATH%;%CD%\poppler\Library\bin

REM Activate virtual environment and run application
echo Starting application...
call venv\Scripts\activate.bat
python app.py

REM If application exits abnormally, keep window open
if %errorlevel% neq 0 (
    echo.
    echo Application exited abnormally with error code: %errorlevel%
    pause
)

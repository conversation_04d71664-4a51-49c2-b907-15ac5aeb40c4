@echo off
echo Simple installation script for PDF Editor
echo This script avoids compilation issues by using pre-compiled packages

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed! Please install Python 3.8 or higher.
    echo You can download Python from https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Create virtual environment directly with venv module
echo Creating virtual environment...
if not exist venv (
    python -m venv venv
) else (
    echo Virtual environment already exists, skipping creation.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Upgrade pip to latest version
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install wheel package first
echo Installing wheel package...
python -m pip install wheel

REM Install packages one by one with --only-binary option
echo Installing dependencies (this may take a few minutes)...
python -m pip install --only-binary=:all: Flask==2.0.1
python -m pip install --only-binary=:all: flask-cors==3.0.10
python -m pip install --only-binary=:all: Werkzeug==2.0.1
python -m pip install --only-binary=:all: PyMuPDF==1.18.17
python -m pip install --only-binary=:all: opencv-python-headless==********
python -m pip install --only-binary=:all: numpy==1.19.5
python -m pip install --only-binary=:all: Pillow==8.3.2
python -m pip install --only-binary=:all: pdf2image==1.16.0
python -m pip install --only-binary=:all: python-docx==0.8.11
python -m pip install --only-binary=:all: pdf2docx==0.5.6

REM Create necessary directories
echo Creating necessary directories...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

REM Download Poppler manually
echo.
echo =====================================================================
echo Please download and install Poppler manually:
echo 1. Download Poppler from: 
echo    https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
echo 2. Extract the downloaded file
echo 3. Rename the extracted folder to "poppler"
echo 4. Place the "poppler" folder in the current directory
echo =====================================================================
echo.

set /p answer=Have you completed the Poppler installation? (y/n): 
if /i "%answer%" neq "y" (
    echo Please complete the Poppler installation before running the application.
    pause
    exit /b 1
)

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo Poppler installation not detected correctly. Please ensure:
    echo 1. You have downloaded and extracted Poppler
    echo 2. The extracted folder is renamed to "poppler"
    echo 3. The "poppler" folder is placed in the current directory
    echo 4. The folder structure is correct (should include Library\bin subdirectory)
    pause
    exit /b 1
)

echo.
echo Installation complete!
echo Use run_ascii.bat to start the application.
echo.
pause

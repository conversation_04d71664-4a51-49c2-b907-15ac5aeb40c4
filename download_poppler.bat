@echo off
echo This script will help you download and install Poppler.
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available.'" > nul 2>&1
if %errorlevel% neq 0 (
    echo PowerShell is not available on this system.
    echo Please download Poppler manually:
    echo 1. Open a web browser
    echo 2. Go to: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
    echo 3. Save the file to this directory
    echo 4. Extract the ZIP file
    echo 5. Rename the extracted folder to "poppler"
    pause
    exit /b 1
)

echo PowerShell is available. Attempting to download Poppler...
echo.

REM Try to download using PowerShell
powershell -ExecutionPolicy Bypass -File download_poppler.ps1

if %errorlevel% neq 0 (
    echo.
    echo PowerShell download failed.
    echo.
    echo Please download Poppler manually:
    echo 1. Open a web browser
    echo 2. Go to: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
    echo 3. Save the file to this directory
    echo 4. Extract the ZIP file
    echo 5. Rename the extracted folder to "poppler"
) else (
    echo.
    echo Poppler has been successfully downloaded and installed.
)

echo.
pause

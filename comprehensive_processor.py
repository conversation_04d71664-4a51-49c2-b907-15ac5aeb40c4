import fitz
import logging
import os
import cv2
import numpy as np
from PIL import Image, ImageDraw
import io
import shutil
import traceback
from pdf2image import convert_from_path

def is_scanned_pdf(input_path):
    try:
        doc = fitz.open(input_path)
        page_count = min(2, len(doc))
        total_text_length = 0

        for page_num in range(page_count):
            page = doc[page_num]
            text = page.get_text()
            total_text_length += len(text.strip())

        doc.close()
        avg_text_length = total_text_length / page_count if page_count > 0 else 0
        is_scanned = avg_text_length < 100

        logging.info(f"PDF类型检测: {input_path}, 平均文本长度: {avg_text_length:.1f}, 判断结果: {'扫描型' if is_scanned else '文本型'}")

        return is_scanned

    except Exception as e:
        logging.error(f"检测PDF类型时出错: {str(e)}")
        return True

def analyze_redhead_comprehensive(page, matrix=2.0):
    """全面分析页面中的红头位置，使用多种方法"""
    # 渲染页面为图像
    pix = page.get_pixmap(matrix=fitz.Matrix(matrix, matrix))
    img_data = pix.samples
    img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
    img_np = np.array(img)

    # 获取页面尺寸
    height, width = img_np.shape[:2]

    # 1. 基于文本的红头检测 - 更全面的方法
    text_blocks = page.get_text("dict")["blocks"]
    red_texts = []
    all_texts = []  # 所有文本，用于辅助判断

    # 第一遍：收集所有文本和红色文本
    for block in text_blocks:
        if "lines" in block:
            for line in block["lines"]:
                for span in line["spans"]:
                    bbox = span.get("bbox", [0, 0, 0, 0])
                    x0, y0, x1, y1 = bbox
                    rel_y0 = y0 / page.rect.height

                    # 只考虑页面前50%的文本
                    if rel_y0 < 0.5:
                        all_texts.append({
                            "text": span["text"],
                            "bbox": bbox,
                            "y": y0,
                            "y1": y1,
                            "color": span.get("color", 0)
                        })

                        # 检查是否是红色
                        color = span.get("color", 0)
                        r = (color >> 16) & 0xFF
                        g = (color >> 8) & 0xFF
                        b = color & 0xFF

                        # 更宽松的红色检测条件
                        if (r > 100 and r > g * 1.2 and r > b * 1.2) or (r > 180 and g < 180 and b < 180):
                            red_texts.append({
                                "text": span["text"],
                                "bbox": bbox,
                                "y": y0,
                                "y1": y1,
                                "color": (r, g, b)
                            })

    if red_texts:
        # 找到最下面的红色文本
        max_y = max(text["y1"] for text in red_texts)

        # 查找红色文本下方的第一个非红色文本
        non_red_texts = [t for t in all_texts if t["y"] > max_y and t not in red_texts]
        if non_red_texts:
            # 按y坐标排序
            non_red_texts.sort(key=lambda t: t["y"])
            # 取第一个非红色文本的y坐标作为红头底部
            next_text_y = non_red_texts[0]["y"]
            # 红头高度为最后一个红色文本和第一个非红色文本之间的中点
            redhead_height = (max_y + next_text_y) / 2
        else:
            # 如果没有找到非红色文本，增加一个固定的边距
            redhead_height = max_y + 20

        # 确保红头高度至少为页面高度的15%
        min_height = page.rect.height * 0.15
        redhead_height = max(redhead_height, min_height)

        # 确保红头高度不超过页面高度的50%
        max_height = page.rect.height * 0.5
        redhead_height = min(redhead_height, max_height)

        return {
            "x": 0,
            "y": 0,
            "width": page.rect.width,
            "height": redhead_height,
            "method": "text_based",
            "texts": red_texts,
            "text_count": len(red_texts)
        }

    # 2. 基于图像的红头检测 - 更全面的方法
    # 转换为HSV色彩空间，更容易检测红色
    img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

    # 定义红色范围（HSV空间中的两个范围）
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    # 创建红色掩码
    mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
    red_mask = mask1 + mask2

    # 使用形态学操作连接相近的红色区域
    kernel = np.ones((5, 5), np.uint8)
    red_mask_dilated = cv2.dilate(red_mask, kernel, iterations=2)
    red_mask_eroded = cv2.erode(red_mask_dilated, kernel, iterations=1)

    # 查找红色区域的轮廓
    contours, _ = cv2.findContours(red_mask_eroded, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 筛选顶部的大轮廓
    top_contours = []
    min_area = width * 20  # 最小面积要求

    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_area:
            continue

        # 获取轮廓的边界框
        x, y, w, h = cv2.boundingRect(contour)

        # 只考虑页面顶部的轮廓（前50%）
        if y < height * 0.5:
            top_contours.append((x, y, w, h, area))

    if top_contours:
        # 按面积排序
        top_contours.sort(key=lambda c: c[4], reverse=True)

        # 合并重叠的轮廓
        merged_contours = []
        for contour in top_contours:
            x, y, w, h, area = contour

            # 检查是否与已有轮廓重叠
            overlap = False
            for i, merged in enumerate(merged_contours):
                mx, my, mw, mh, marea = merged

                # 检查重叠
                if (x < mx + mw and x + w > mx and y < my + mh and y + h > my):
                    # 合并轮廓
                    new_x = min(x, mx)
                    new_y = min(y, my)
                    new_w = max(x + w, mx + mw) - new_x
                    new_h = max(y + h, my + mh) - new_y
                    new_area = area + marea

                    merged_contours[i] = (new_x, new_y, new_w, new_h, new_area)
                    overlap = True
                    break

            if not overlap:
                merged_contours.append(contour)

        if merged_contours:
            # 获取所有合并轮廓的边界
            all_x = [c[0] for c in merged_contours]
            all_y = [c[1] for c in merged_contours]
            all_right = [c[0] + c[2] for c in merged_contours]
            all_bottom = [c[1] + c[3] for c in merged_contours]

            # 计算包含所有轮廓的最小矩形
            min_x = min(all_x) if all_x else 0
            min_y = min(all_y) if all_y else 0
            max_right = max(all_right) if all_right else width
            max_bottom = max(all_bottom) if all_bottom else height * 0.2

            # 计算宽度和高度
            w_combined = max_right - min_x
            h_combined = max_bottom - min_y

            # 转换回原始页面坐标
            scale = 1.0 / matrix
            x_orig = min_x * scale
            y_orig = min_y * scale
            w_orig = w_combined * scale
            h_orig = h_combined * scale

            # 稍微扩大高度，确保覆盖所有红头文字
            h_orig = h_orig * 1.3

            # 确保红头高度至少为页面高度的15%
            min_height = page.rect.height * 0.15
            h_orig = max(h_orig, min_height)

            # 确保红头高度不超过页面高度的50%
            max_height = page.rect.height * 0.5
            h_orig = min(h_orig, max_height)

            # 如果红头宽度小于页面宽度的80%，扩展到整个页面宽度
            if w_orig < page.rect.width * 0.8:
                x_orig = 0
                w_orig = page.rect.width

            return {
                "x": x_orig,
                "y": y_orig,
                "width": w_orig,
                "height": h_orig,
                "method": "contour_based",
                "area": sum(c[4] for c in merged_contours)
            }

    # 3. 基于行扫描的红头检测 - 更全面的方法
    # 计算每行的红色像素数量
    row_sums = np.sum(red_mask_eroded, axis=1)

    # 找到红色像素数量最多的行
    max_row = np.argmax(row_sums)

    # 只考虑页面顶部的行（前50%）
    if max_row < height * 0.5 and row_sums[max_row] > width * 0.05:
        # 向下扫描，找到红头的底部
        bottom_row = max_row
        threshold = width * 0.02  # 降低阈值，更宽松的检测条件

        # 向下扫描直到找到连续3行红色像素数量低于阈值的位置
        consecutive_low = 0
        while bottom_row < height * 0.5 and consecutive_low < 3:
            if row_sums[bottom_row] < threshold:
                consecutive_low += 1
            else:
                consecutive_low = 0
            bottom_row += 1

        # 向上扫描，找到红头的顶部
        top_row = max_row
        consecutive_low = 0
        while top_row > 0 and consecutive_low < 3:
            if row_sums[top_row] < threshold:
                consecutive_low += 1
            else:
                consecutive_low = 0
            top_row -= 1

        # 计算红头高度
        redhead_height = bottom_row - top_row

        # 转换回原始页面坐标
        scale = 1.0 / matrix
        y_orig = max(0, top_row) * scale
        h_orig = redhead_height * scale

        # 稍微扩大高度，确保覆盖所有红头文字
        h_orig = h_orig * 1.3

        # 确保红头高度至少为页面高度的15%
        min_height = page.rect.height * 0.15
        h_orig = max(h_orig, min_height)

        # 确保红头高度不超过页面高度的50%
        max_height = page.rect.height * 0.5
        h_orig = min(h_orig, max_height)

        return {
            "x": 0,
            "y": y_orig,
            "width": page.rect.width,
            "height": h_orig,
            "method": "row_scan",
            "area": redhead_height * width
        }

    # 4. 基于颜色分析的红头检测 - 更全面的方法
    # 分析页面顶部区域的颜色
    top_region = img_np[:int(height * 0.5), :]

    # 分析不同高度的区域
    height_ratios = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
    best_height_ratio = 0.2  # 默认值
    best_redness = 0  # 红色程度

    for ratio in height_ratios:
        region = img_np[:int(height * ratio), :]
        r_mean = np.mean(region[:,:,0])
        g_mean = np.mean(region[:,:,1])
        b_mean = np.mean(region[:,:,2])

        # 计算红色程度
        redness = r_mean - (g_mean + b_mean) / 2

        # 检查是否偏红
        if r_mean > 180 and redness > best_redness:
            best_redness = redness
            best_height_ratio = ratio

    # 检查顶部区域是否偏红
    r_mean = np.mean(top_region[:,:,0])
    g_mean = np.mean(top_region[:,:,1])
    b_mean = np.mean(top_region[:,:,2])
    is_reddish = r_mean > 180 and r_mean > g_mean * 1.03 and r_mean > b_mean * 1.03

    if is_reddish or best_redness > 5:
        # 设置红头高度为找到的最佳比例
        h_orig = page.rect.height * best_height_ratio

        # 确保红头高度至少为页面高度的15%
        min_height = page.rect.height * 0.15
        h_orig = max(h_orig, min_height)

        # 确保红头高度不超过页面高度的50%
        max_height = page.rect.height * 0.5
        h_orig = min(h_orig, max_height)

        return {
            "x": 0,
            "y": 0,
            "width": page.rect.width,
            "height": h_orig,
            "method": "color_analysis",
            "color": (r_mean, g_mean, b_mean),
            "redness": best_redness,
            "height_ratio": best_height_ratio
        }

    # 5. 固定位置的红头检测（最后的备选方案）
    # 假设红头在页面顶部20%的位置
    h_orig = page.rect.height * 0.2

    # 确保红头高度至少为页面高度的15%
    min_height = page.rect.height * 0.15
    h_orig = max(h_orig, min_height)

    return {
        "x": 0,
        "y": 0,
        "width": page.rect.width,
        "height": h_orig,
        "method": "fixed_position"
    }

def analyze_stamps_comprehensive(page, matrix=2.0):
    """全面分析页面中的公章位置，使用多种方法"""
    # 渲染页面为图像
    pix = page.get_pixmap(matrix=fitz.Matrix(matrix, matrix))
    img_data = pix.samples
    img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
    img_np = np.array(img)

    # 获取页面尺寸
    height, width = img_np.shape[:2]

    # 1. 基于图像的公章检测
    # 转换为HSV色彩空间，更容易检测红色
    img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

    # 定义红色范围（HSV空间中的两个范围）
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    # 创建红色掩码
    mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
    red_mask = mask1 + mask2

    # 使用形态学操作连接相近的红色区域
    kernel = np.ones((3, 3), np.uint8)
    red_mask_dilated = cv2.dilate(red_mask, kernel, iterations=1)

    # 查找红色区域的轮廓
    contours, _ = cv2.findContours(red_mask_dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 筛选圆形轮廓（公章）
    stamps = []
    min_area = 5000  # 最小面积要求

    for contour in contours:
        area = cv2.contourArea(contour)
        if area < min_area:
            continue

        # 计算轮廓的圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue

        circularity = 4 * np.pi * area / (perimeter * perimeter)

        # 公章通常是圆形的
        if circularity > 0.7:  # 圆形度阈值
            # 获取轮廓的边界框
            x, y, w, h = cv2.boundingRect(contour)

            # 确保宽高比接近1（圆形）
            aspect_ratio = float(w) / h
            if 0.8 <= aspect_ratio <= 1.2:
                stamps.append({
                    "x": x,
                    "y": y,
                    "width": w,
                    "height": h,
                    "circularity": circularity,
                    "area": area,
                    "method": "contour_based"
                })

    # 2. 基于图像的公章检测（直接查找页面上的图像）
    img_list = page.get_images(full=True)
    for img_index, img in enumerate(img_list):
        try:
            # 获取图像信息
            xref = img[0]

            # 获取图像位置和尺寸
            for item in page.get_image_info():
                if item["xref"] == xref:
                    bbox = item.get("bbox", [0, 0, 0, 0])
                    if bbox:
                        x0, y0, x1, y1 = bbox
                        w = x1 - x0
                        h = y1 - y0

                        # 检查是否可能是公章（圆形或正方形）
                        aspect_ratio = float(w) / h if h > 0 else 0
                        if 0.8 <= aspect_ratio <= 1.2 and w > 50 and h > 50:
                            stamps.append({
                                "x": x0,
                                "y": y0,
                                "width": w,
                                "height": h,
                                "circularity": 1.0,  # 假设是完美的圆形
                                "area": w * h,
                                "method": "image_based",
                                "xref": xref
                            })
        except Exception as e:
            logging.warning(f"处理图像时出错: {str(e)}")

    # 转换回原始页面坐标
    scale = 1.0 / matrix
    for stamp in stamps:
        stamp["x"] *= scale
        stamp["y"] *= scale
        stamp["width"] *= scale
        stamp["height"] *= scale

    return stamps

def remove_irregular_lines(image):
    """
    专门用于去除图像中的凌乱线条，同时保留文字
    """
    # 创建一个副本，避免修改原始图像
    cleaned = image.copy()

    # 1. 使用形态学操作检测和清理细线条
    # 创建不同方向的结构元素
    kernel_h = np.ones((1, 7), np.uint8)  # 水平线
    kernel_v = np.ones((7, 1), np.uint8)  # 垂直线
    kernel_d1 = np.eye(5, dtype=np.uint8)  # 对角线 \
    kernel_d2 = np.flip(np.eye(5, dtype=np.uint8), 0)  # 对角线 /

    # 使用开运算检测各个方向的线条
    h_opened = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_h)
    v_opened = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_v)
    d1_opened = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_d1)
    d2_opened = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel_d2)

    # 合并所有方向的线条检测结果
    all_lines = cv2.bitwise_or(
        cv2.bitwise_or(255 - h_opened, 255 - v_opened),
        cv2.bitwise_or(255 - d1_opened, 255 - d2_opened)
    )

    # 只保留线条部分（黑色像素）
    lines_mask = 255 - all_lines

    # 将检测到的线条区域设为白色
    cleaned[lines_mask < 127] = 255

    # 2. 使用霍夫线变换检测直线
    edges = cv2.Canny(cleaned, 30, 150, apertureSize=3)
    lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=40,
                           minLineLength=40, maxLineGap=15)

    if lines is not None and len(lines) > 0:
        # 创建线条掩码
        line_mask = np.zeros_like(cleaned)

        # 在掩码上绘制所有检测到的线条
        for line in lines:
            x1, y1, x2, y2 = line[0]
            cv2.line(line_mask, (x1, y1), (x2, y2), 255, 5)  # 增加线宽以确保覆盖

        # 将线条区域的黑色像素设为白色
        line_black_pixels = np.logical_and(cleaned == 0, line_mask == 255)
        cleaned[line_black_pixels] = 255

    # 3. 使用连通区域分析清理细小线条和噪点
    # 标记连通区域
    num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(
        cv2.bitwise_not(cleaned),
        connectivity=8
    )

    # 清理细小线条和噪点
    for i in range(1, num_labels):  # 跳过背景（标签0）
        area = stats[i, cv2.CC_STAT_AREA]
        width = stats[i, cv2.CC_STAT_WIDTH]
        height = stats[i, cv2.CC_STAT_HEIGHT]

        # 计算宽高比
        aspect_ratio = max(width, height) / (min(width, height) if min(width, height) > 0 else 1)

        # 如果是细长的线条或小噪点，则清理
        if (aspect_ratio > 8 and area < 500) or area < 20:
            cleaned[labels == i] = 255

    return cleaned

def calculate_skew_angle(image):
    """
    计算图像的倾斜角度
    """
    # 检测边缘
    edges = cv2.Canny(image, 50, 150, apertureSize=3)

    # 使用霍夫变换检测直线
    lines = cv2.HoughLines(edges, 1, np.pi/180, 100)

    # 如果没有检测到线条，返回0度
    if lines is None or len(lines) == 0:
        return 0

    # 计算所有线条的角度
    angles = []
    for line in lines:
        rho, theta = line[0]
        # 只考虑接近水平的线条（接近0度或180度）
        if (theta < np.pi/4) or (theta > 3*np.pi/4):
            angle = theta - np.pi/2 if theta > np.pi/4 else theta
            angles.append(angle)

    # 如果没有合适的线条，返回0度
    if not angles:
        return 0

    # 返回中位数角度（弧度转换为度）
    median_angle = np.median(angles) * 180 / np.pi

    # 限制角度范围，避免过度旋转
    if abs(median_angle) > 10:
        return 0  # 如果角度太大，可能是误检测，返回0

    return median_angle

def rotate_image(image, angle):
    """
    旋转图像
    """
    # 如果角度接近0，不旋转
    if abs(angle) < 0.5:
        return image

    # 获取图像尺寸
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)

    # 计算旋转矩阵
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # 执行旋转
    rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

    return rotated

def process_scanned_pdf(input_path, output_path):
    """
    处理扫描型PDF - 使用图像处理方法去除背景，增强文字清晰度
    实现灰度转换、去噪与锐化、二值化处理、倾斜矫正和边缘裁剪
    优化版：改进了背景去除效果，减小了文件大小，消除了不规则线条
    """
    try:
        logging.info(f"处理扫描型PDF: {input_path}")

        # 打开PDF文件
        doc = fitz.open(input_path)
        output_doc = fitz.open()  # 创建新的PDF文档

        # 处理每一页
        for page_num in range(len(doc)):
            page = doc[page_num]

            # 创建新页面
            new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)

            # 渲染页面为高分辨率图像，但不要太高以控制内存使用
            pix = page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))
            img_data = pix.samples
            img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
            img_np = np.array(img)

            # 检测页面中的红色区域
            img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

            # 定义红色范围（HSV空间中的两个范围）
            lower_red1 = np.array([0, 70, 70])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 70, 70])
            upper_red2 = np.array([180, 255, 255])

            # 创建红色掩码
            mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
            red_mask = cv2.bitwise_or(mask1, mask2)

            # 检查是否有足够的红色像素
            red_pixels = cv2.countNonZero(red_mask)

            # 1. 灰度转换
            gray = cv2.cvtColor(img_np, cv2.COLOR_BGR2GRAY)

            # 2. 去噪与锐化
            blurred = cv2.GaussianBlur(gray, (5,5), 0)
            sharpened = cv2.addWeighted(gray, 1.5, blurred, -0.5, 0)

            # 3. 高级背景去除方案
            # 步骤1: 局部对比度增强
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            enhanced = clahe.apply(sharpened)

            # 步骤2: 多尺度纹理分析
            bg_mask = np.zeros_like(enhanced)
            for scale in [3, 5, 7]:
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (scale, scale))
                tophat = cv2.morphologyEx(enhanced, cv2.MORPH_TOPHAT, kernel)
                bg_mask = cv2.bitwise_or(bg_mask, tophat)

            # 步骤3: 非局部均值去噪
            denoised = cv2.fastNlMeansDenoising(enhanced, h=15, templateWindowSize=7, searchWindowSize=21)

            # 步骤4: 智能背景分离
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            binary = cv2.bitwise_and(binary, cv2.bitwise_not(bg_mask))

            # 步骤5: 图像修复
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3,3))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=2)
            binary = cv2.inpaint(enhanced, binary, 3, cv2.INPAINT_TELEA)

            # 4. 倾斜矫正
            angle = calculate_skew_angle(binary)
            rotated = rotate_image(binary, angle)

            # 5. 边缘裁剪
            contours, _ = cv2.findContours(rotated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            max_contour = max(contours, key=cv2.contourArea)
            x,y,w,h = cv2.boundingRect(max_contour)
            cropped = rotated[y:y+h, x:x+w]

            # 形态学操作去除细小噪点
            kernel = np.ones((3, 3), np.uint8)
            cleaned = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel, iterations=2)

            # 使用闭运算连接断开的线条
            kernel_close = np.ones((1, 1), np.uint8)
            cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_CLOSE, kernel_close, iterations=1)

            # 然后进行高斯模糊去除高频噪声
            blurred = cv2.GaussianBlur(cleaned, (3, 3), 0)

            # 使用非局部均值去噪进一步减少噪点，保留边缘
            denoised = cv2.fastNlMeansDenoising(blurred, None, 10, 7, 21)

            # 使用USM锐化算法增强边缘
            gaussian = cv2.GaussianBlur(denoised, (0, 0), 3.0)
            sharpened = cv2.addWeighted(denoised, 1.5, gaussian, -0.5, 0)

            # 3. 改进的二值化处理
            # 使用Otsu自适应阈值方法，更好地分离前景和背景
            _, binary_otsu = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 使用自适应阈值进一步增强局部对比度
            binary_adaptive = cv2.adaptiveThreshold(
                sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY, 15, 5
            )

            # 结合两种二值化结果
            binary = cv2.bitwise_and(binary_otsu, binary_adaptive)

            # 使用形态学操作去除小噪点
            kernel = np.ones((2, 2), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

            # 使用闭运算连接断开的线条
            kernel_close = np.ones((2, 2), np.uint8) # Keep this for connecting broken text
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel_close)

            # 进一步使用开运算去除背景噪点和细小线条
            # 尝试使用稍大一点的核来去除线条
            kernel_open = np.ones((3, 3), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_open, iterations=1) # iterations can be adjusted

            # 再次进行一次闭运算，尝试修复因开运算可能导致的文字断裂
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel_close, iterations=1)

            # 4. 倾斜矫正
            angle = calculate_skew_angle(binary)  # 使用前面实现的角度检测函数
            rotated = rotate_image(binary, angle)  # 使用前面实现的旋转函数

            # 5. 边缘裁剪 - 改进的边缘检测和裁剪
            # 查找轮廓
            contours, _ = cv2.findContours(rotated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 如果找到轮廓，进行裁剪
            if contours and len(contours) > 0:
                # 找到最大轮廓
                max_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(max_contour)

                # 确保裁剪区域不会太小
                if w > rotated.shape[1] * 0.5 and h > rotated.shape[0] * 0.5:
                    # 添加小边距避免裁剪过紧
                    margin = 5
                    x = max(0, x - margin)
                    y = max(0, y - margin)
                    w = min(rotated.shape[1] - x, w + 2*margin)
                    h = min(rotated.shape[0] - y, h + 2*margin)
                    cropped = rotated[y:y+h, x:x+w]
                else:
                    # 如果裁剪区域太小，使用原图
                    cropped = rotated
            else:
                # 如果没有找到轮廓，使用原图
                cropped = rotated

            # 确保是白底黑字
            if np.mean(cropped) < 127:
                cropped = cv2.bitwise_not(cropped)

            # 转换回RGB格式以便保存到PDF
            processed_img = cv2.cvtColor(cropped, cv2.COLOR_GRAY2RGB)

            # 将处理后的图像转换为PIL图像
            pil_img = Image.fromarray(processed_img)

            # 如果检测到足够的红色像素，处理红色区域
            if red_pixels > 1000:
                logging.info(f"扫描型PDF检测到红色区域: 页面{page_num+1}, {red_pixels}个红色像素")

                # 创建一个RGB图像的副本
                img_processed = img_np.copy()

                # 使用改进的形态学操作连接相近的红色区域，但避免过度膨胀
                kernel = np.ones((7, 7), np.uint8)  # 减小核大小，避免过度连接
                red_mask_dilated = cv2.dilate(red_mask, kernel, iterations=1)  # 减少迭代次数

                # 使用开运算去除小噪点
                red_mask_cleaned = cv2.morphologyEx(red_mask_dilated, cv2.MORPH_OPEN, np.ones((3, 3), np.uint8))

                # 查找红色区域的轮廓
                contours, _ = cv2.findContours(red_mask_cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # 筛选顶部的大轮廓
                img_height, img_width = img_np.shape[:2]
                top_contours = []

                for contour in contours:
                    area = cv2.contourArea(contour)
                    if area < img_width * 8:  # 稍微降低最小面积要求
                        continue

                    # 获取轮廓的边界框
                    x, y, w, h = cv2.boundingRect(contour)

                    # 只考虑页面顶部的轮廓（前40%）
                    if y < img_height * 0.4:
                        top_contours.append((x, y, w, h, area))

                # 如果找到了顶部轮廓
                if top_contours:
                    # 按面积排序
                    top_contours.sort(key=lambda c: c[4], reverse=True)

                    # 获取最大的轮廓
                    x, y, w, h = top_contours[0][:4]

                    # 创建一个白色矩形覆盖整个红头区域
                    # 确保覆盖整个红头区域，但不要覆盖太多
                    max_height = int(img_height * 0.2)  # 最大高度为页面高度的20%
                    h = min(h, max_height)

                    # 在图像上绘制白色矩形，使用渐变边缘避免硬边界
                    # 先创建一个纯白色矩形
                    mask = np.zeros((img_height, img_width), dtype=np.uint8)
                    cv2.rectangle(mask, (0, 0), (img_width, y + h), 255, -1)

                    # 对掩码进行高斯模糊，创建渐变边缘
                    blur_size = 9  # 增大模糊半径
                    mask_blurred = cv2.GaussianBlur(mask, (blur_size, blur_size), 0)

                    # 将模糊后的掩码应用到图像
                    for c in range(3):
                        img_processed[:,:,c] = np.where(
                            mask_blurred > 0,
                            255,  # 白色
                            img_processed[:,:,c]
                        )

                    logging.info(f"使用渐变白色矩形覆盖红头区域: 页面{page_num+1}, 高度{h}像素")
                else:
                    # 如果没有找到明确的红头轮廓，使用改进的方法
                    # 查找红色像素的垂直分布
                    red_pixel_rows = np.sum(red_mask_cleaned, axis=1)

                    # 找出红色像素集中的行
                    red_rows = np.where(red_pixel_rows > img_width * 0.08)[0]  # 稍微降低阈值

                    if len(red_rows) > 0:
                        # 找出红色区域的上下边界
                        top_row = np.min(red_rows)
                        bottom_row = np.max(red_rows)

                        # 确保高度合理
                        max_height = int(img_height * 0.2)
                        height = min(bottom_row - top_row + 20, max_height)

                        # 创建渐变边缘的白色矩形
                        mask = np.zeros((img_height, img_width), dtype=np.uint8)
                        cv2.rectangle(mask, (0, top_row), (img_width, top_row + height), 255, -1)

                        # 对掩码进行高斯模糊，创建渐变边缘
                        blur_size = 9
                        mask_blurred = cv2.GaussianBlur(mask, (blur_size, blur_size), 0)

                        # 将模糊后的掩码应用到图像
                        for c in range(3):
                            img_processed[:,:,c] = np.where(
                                mask_blurred > 0,
                                255,  # 白色
                                img_processed[:,:,c]
                            )

                        logging.info(f"使用渐变白色矩形覆盖红头区域: 页面{page_num+1}, 从{top_row}行到{top_row+height}行")

                # 将处理后的图像转换为PIL图像
                red_processed_img = Image.fromarray(img_processed)

                # 合并处理后的二值化图像和红头处理图像
                # 使用二值化图像的文本清晰度和红头处理图像的背景清洁度
                pil_img = red_processed_img

                logging.info(f"已处理扫描型PDF的红色区域: 页面{page_num+1}, 图像质量已优化")

            # 降低图像分辨率以减小文件大小，但保持较高的清晰度
            # 计算新的尺寸，保持宽高比
            orig_width, orig_height = pil_img.size
            # 调整scale_factor以进一步减小文件大小，同时尝试保持可接受的清晰度
            scale_factor = 0.75  # 从0.85调整到0.75
            new_width = int(orig_width * scale_factor)
            new_height = int(orig_height * scale_factor)

            # 使用高质量的重采样方法调整图像大小
            pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS) # Lanczos is good for downscaling

            # 使用JBIG2压缩算法或高效的JPEG压缩来保存图像
            img_bytes = io.BytesIO()

            # 对于黑白文本页面，使用高压缩比的PNG格式 (或者考虑转换为1-bit TIFF with CCITT G4)
            # 对于含有红头的页面，使用优化的JPEG格式
            if red_pixels > 1000:
                # 使用优化的JPEG格式，平衡质量和文件大小
                pil_img.save(img_bytes, format="JPEG", quality=75, optimize=True, progressive=True) # quality从85调整到75
            else:
                # 对于纯黑白图像，转换为1-bit图像可以极大减小体积
                # 如果pil_img是灰度图，先转为 'L'，再转为 '1'
                if pil_img.mode != '1':
                    pil_img_bw = pil_img.convert('L').convert('1', dither=Image.NONE)
                else:
                    pil_img_bw = pil_img
                # 使用PNG格式，针对黑白文本优化，或者考虑TIFF G4
                # PNG对于1-bit图像压缩效果也很好
                pil_img_bw.save(img_bytes, format="PNG", optimize=True, compress_level=9) # compress_level 9 is max

            img_bytes.seek(0)

            # 插入处理后的图像，调整大小以适应页面
            new_page.insert_image(new_page.rect, stream=img_bytes.getvalue())

            logging.info(f"已处理扫描型PDF页面 {page_num+1}，应用了灰度转换、去噪锐化、二值化、倾斜矫正和边缘裁剪")


            # 处理其他页面的公章
            # 转换为HSV色彩空间
            img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

            # 定义红色范围
            lower_red1 = np.array([0, 50, 50])
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 50, 50])
            upper_red2 = np.array([180, 255, 255])

            # 创建红色掩码
            mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
            red_mask = mask1 + mask2

            # 查找红色区域的轮廓
            contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 筛选可能的公章轮廓
            stamp_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area < 1000:  # 忽略太小的区域
                    continue

                # 计算轮廓的圆形度
                perimeter = cv2.arcLength(contour, True)
                if perimeter == 0:
                    continue

                circularity = 4 * np.pi * area / (perimeter * perimeter)

                # 获取轮廓的边界框
                x, y, w, h = cv2.boundingRect(contour)

                # 计算宽高比
                aspect_ratio = float(w) / h if h > 0 else 0

                # 公章通常是圆形的
                if circularity > 0.6 and 0.8 <= aspect_ratio <= 1.2:
                    stamp_contours.append(contour)

            if stamp_contours:
                logging.info(f"扫描型PDF检测到{len(stamp_contours)}个公章: 页面{page_num+1}")

                # 创建一个RGB图像的副本
                img_processed = img_np.copy()

                # 处理每个公章轮廓
                for contour in stamp_contours:
                    # 获取公章的边界框
                    x, y, w, h = cv2.boundingRect(contour)

                    # 稍微扩大边界框，确保完全覆盖公章
                    padding = 5
                    x_start = max(0, x - padding)
                    y_start = max(0, y - padding)
                    x_end = min(img_np.shape[1], x + w + padding)
                    y_end = min(img_np.shape[0], y + h + padding)

                    # 提取公章区域
                    stamp_region = img_np[y_start:y_end, x_start:x_end].copy()

                    # 不需要转换为HSV色彩空间，直接在RGB空间中工作

                    # 使用简单直接的方法处理公章
                    # 创建一个白色背景
                    white_bg = np.ones_like(stamp_region) * 255

                    # 定义红色范围（RGB空间）- 直接在RGB空间中工作，避免颜色空间转换
                    is_red = (
                        (stamp_region[:,:,0] > 150) &  # R通道较高
                        (stamp_region[:,:,0] > stamp_region[:,:,1] * 1.5) &  # R明显大于G
                        (stamp_region[:,:,0] > stamp_region[:,:,2] * 1.5)    # R明显大于B
                    )

                    # 定义黑色范围（RGB空间）- 简单明确地定义黑色
                    is_black = (
                        (stamp_region[:,:,0] < 80) &  # R通道较低
                        (stamp_region[:,:,1] < 80) &  # G通道较低
                        (stamp_region[:,:,2] < 80)    # B通道较低
                    )

                    # 创建红色掩码和黑色掩码
                    red_mask = is_red.astype(np.uint8) * 255
                    black_mask = is_black.astype(np.uint8) * 255

                    # 直接复制黑色文字到白色背景
                    for c in range(3):  # 对RGB三个通道分别处理
                        # 只在黑色文字区域保留原始像素，其他区域保持白色
                        white_bg[:, :, c] = np.where(
                            black_mask > 0,
                            stamp_region[:, :, c],  # 保留原始黑色文字
                            white_bg[:, :, c]       # 保持白色背景
                        )

                    # 对于红色区域，直接设为白色（不处理红色区域内的黑色文字）
                    for c in range(3):  # 对RGB三个通道分别处理
                        white_bg[:, :, c] = np.where(
                            red_mask > 0,
                            255,  # 白色
                            white_bg[:, :, c]
                        )

                    # 将处理后的公章区域放回原图
                    img_processed[y_start:y_end, x_start:x_end] = white_bg

                    logging.info(f"已处理公章: 页面{page_num+1}, 位置({x},{y}), 尺寸{w}x{h}")

                # 将处理后的图像转换为PIL图像
                pil_img = Image.fromarray(img_processed)

                # 降低图像分辨率以减小文件大小，但保持较高的清晰度
                # 计算新的尺寸，保持宽高比
                orig_width, orig_height = pil_img.size
                scale_factor = 0.9  # 缩小到原来的90%，保持高清晰度
                new_width = int(orig_width * scale_factor)
                new_height = int(orig_height * scale_factor)

                # 使用高质量的重采样方法调整图像大小
                pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS)

                # 使用PNG格式保存公章页面，确保黑色文字清晰
                img_bytes = io.BytesIO()
                pil_img.save(img_bytes, format="PNG", optimize=True)
                img_bytes.seek(0)

                # 插入处理后的图像，调整大小以适应页面
                new_page.insert_image(new_page.rect, stream=img_bytes.getvalue())
                logging.info(f"已处理扫描型PDF的公章: 页面{page_num+1}, 图像质量已优化")
            else:
                # 如果没有检测到公章，直接复制原始页面
                new_page.show_pdf_page(new_page.rect, doc, page_num)
                logging.info(f"扫描型PDF页面{page_num+1}没有检测到公章")

        # 保存处理后的文档 - 优化参数提高速度
        output_doc.save(
            output_path,
            garbage=3,         # 减少垃圾收集级别以提高速度
            deflate=True,      # 使用deflate压缩
            clean=True,        # 清理冗余内容
            linear=False       # 关闭线性化以提高保存速度
        )
        output_doc.close()
        doc.close()

        logging.info(f"扫描型PDF处理完成: {output_path}")
        return {"success": True}

    except Exception as e:
        logging.error(f"处理扫描型PDF时出错: {str(e)}")
        # 如果处理失败，复制原始文件
        try:
            shutil.copy(input_path, output_path)
            logging.info(f"由于处理错误，已复制原始文件到: {output_path}")
        except Exception as copy_error:
            logging.error(f"复制原始文件时出错: {str(copy_error)}")

        return {"success": False, "error": str(e)}

def process_scanned_pdf_remove_red(input_path, output_path):
    """
    处理扫描型PDF去红头和公章 - 使用示例.py中的改进算法
    精确识别红色区域内的黑色文字行，只在空白区域填充白色长条
    """
    try:
        logging.info(f"使用改进的扫描型PDF处理方法: {input_path}")

        # 将PDF转换为图像列表
        images = convert_from_path(input_path, dpi=300)

        processed_images = []
        for i, img in enumerate(images):
            logging.info(f"正在处理第{i+1}页")

            # 转换为OpenCV格式
            opencv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

            # 检测红色区域（增强红头检测）
            hsv = cv2.cvtColor(opencv_img, cv2.COLOR_BGR2HSV)

            # 更精确的红头文字检测参数
            lower_red1 = np.array([0, 150, 150])  # 亮红色
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 150, 150])  # 暗红色
            upper_red2 = np.array([180, 255, 255])

            # 创建红色区域掩膜
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = cv2.bitwise_or(mask1, mask2)

            # 增强顶部区域的红头检测（顶部70%区域）
            height = opencv_img.shape[0]
            top_region_mask = np.zeros_like(red_mask)
            top_region_mask[:int(height*0.7), :] = 255
            red_mask = cv2.bitwise_and(red_mask, top_region_mask)

            # 形态学操作增强红头区域
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5,5))
            red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=2)

            # 找到红头轮廓
            red_contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 处理红头区域（完全遮盖）
            for cnt in red_contours:
                x, y, w, h = cv2.boundingRect(cnt)

                # 只处理顶部区域的红头（y坐标在图像顶部70%内）
                if y < opencv_img.shape[0]*0.7:
                    # 检查是否为圆形区域（可能为公章）
                    aspect_ratio = float(w)/h
                    area = cv2.contourArea(cnt)
                    perimeter = cv2.arcLength(cnt, True)
                    circularity = 4 * 3.1416 * area / (perimeter * perimeter) if perimeter > 0 else 0

                    # 改进的公章检测条件 - 更宽松的参数
                    is_potential_stamp = (
                        (circularity > 0.3 and 0.6 <= aspect_ratio <= 1.4 and area > 1000) or  # 标准公章
                        (circularity > 0.4 and 0.5 <= aspect_ratio <= 1.5 and area > 800) or   # 椭圆公章
                        (circularity > 0.2 and 0.7 <= aspect_ratio <= 1.3 and area > 2000)     # 大型公章
                    )

                    # 处理公章区域（分区填充）
                    if is_potential_stamp:
                        logging.info(f"检测到公章: 圆形度={circularity:.3f}, 宽高比={aspect_ratio:.3f}, 面积={area:.0f}")
                        fill_seal_region(opencv_img, x, y, w, h)
                        continue

                    # 完全遮盖整个红头行区域
                    padding = max(20, int(min(w,h)*0.2))  # 更大的边距确保完全覆盖
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(opencv_img.shape[1] - x, w + 2*padding)
                    h = min(opencv_img.shape[0] - y, h + padding)  # 主要向下扩展

                    # 填充纯白色
                    cv2.rectangle(opencv_img, (x, y), (x+w, y+h), (255, 255, 255), -1)

            # 处理公章区域 - 使用改进的公章检测和处理算法
            opencv_img = process_stamps_in_scanned_image_enhanced(opencv_img)

            # 转换回PIL格式
            processed_images.append(Image.fromarray(cv2.cvtColor(opencv_img, cv2.COLOR_BGR2RGB)))

        # 保存为PDF
        if processed_images:
            processed_images[0].save(
                output_path, "PDF",
                resolution=300,
                save_all=True,
                append_images=processed_images[1:]
            )
            logging.info(f"扫描型PDF处理完成: {output_path}")
            return {"success": True}

        return {"success": False, "error": "没有处理任何图像"}

    except Exception as e:
        logging.error(f"处理扫描型PDF时出错: {str(e)}")
        # 如果处理失败，复制原始文件
        try:
            import shutil
            shutil.copy(input_path, output_path)
            logging.info(f"由于处理错误，已复制原始文件到: {output_path}")
        except Exception as copy_error:
            logging.error(f"复制原始文件时出错: {str(copy_error)}")

        return {"success": False, "error": str(e)}

def fill_seal_region(image, x, y, w, h):
    """填充公章区域，保留黑色文字 - 从示例.py移植的改进算法"""
    try:
        # 增加填充条高度到30像素(约2mm)并扩大填充区域
        strip_height = 30

        # 扩大公章区域范围
        padding = int(min(w,h)*0.3)
        x = max(0, x - padding)
        y = max(0, y - padding)
        w = min(image.shape[1] - x, w + 2*padding)
        h = min(image.shape[0] - y, h + 2*padding)

        # 将公章区域转换为HSV色彩空间
        seal_region = image[y:y+h, x:x+w]
        hsv = cv2.cvtColor(seal_region, cv2.COLOR_BGR2HSV)

        # 使用多种红色检测参数，确保捕获所有红色
        # 参数组1：高饱和度红色
        lower_red1_strict = np.array([0, 200, 200])
        upper_red1_strict = np.array([10, 255, 255])
        lower_red2_strict = np.array([170, 200, 200])
        upper_red2_strict = np.array([180, 255, 255])

        # 参数组2：中等饱和度红色
        lower_red1_medium = np.array([0, 120, 120])
        upper_red1_medium = np.array([15, 255, 255])
        lower_red2_medium = np.array([165, 120, 120])
        upper_red2_medium = np.array([180, 255, 255])

        # 调整黑色区域检测参数
        lower_black = np.array([0, 0, 0])
        upper_black = np.array([180, 255, 60])  # 稍微提高亮度阈值以更好保留文字

        # 按strip_height高度分区处理
        for strip_y in range(0, h, strip_height):
            strip_end = min(strip_y + strip_height, h)
            strip = seal_region[strip_y:strip_end, :]

            # 检测红色区域 - 使用多种参数
            hsv_strip = cv2.cvtColor(strip, cv2.COLOR_BGR2HSV)

            # 严格参数检测
            red_mask1_strict = cv2.inRange(hsv_strip, lower_red1_strict, upper_red1_strict)
            red_mask2_strict = cv2.inRange(hsv_strip, lower_red2_strict, upper_red2_strict)
            red_mask_strict = cv2.bitwise_or(red_mask1_strict, red_mask2_strict)

            # 中等参数检测
            red_mask1_medium = cv2.inRange(hsv_strip, lower_red1_medium, upper_red1_medium)
            red_mask2_medium = cv2.inRange(hsv_strip, lower_red2_medium, upper_red2_medium)
            red_mask_medium = cv2.bitwise_or(red_mask1_medium, red_mask2_medium)

            # 合并所有红色检测结果
            red_mask = cv2.bitwise_or(red_mask_strict, red_mask_medium)

            # 形态学操作增强红色区域检测
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=2)

            # 检测黑色区域
            black_mask = cv2.inRange(hsv_strip, lower_black, upper_black)

            # 只填充纯红色区域(没有黑色像素的区域)
            fill_mask = cv2.bitwise_and(red_mask, cv2.bitwise_not(black_mask))

            # 应用填充 - 增加填充强度
            strip[fill_mask > 0] = (255, 255, 255)

            # 二次填充确保完全覆盖
            if np.any(fill_mask):
                strip[cv2.dilate(fill_mask, kernel, iterations=1) > 0] = (255, 255, 255)

        return True
    except Exception as e:
        logging.error(f"填充公章区域时出错: {e}")
        return False

def process_stamps_in_scanned_image_enhanced(image):
    """
    增强版公章处理 - 专门解决2.pdf类型的公章检测问题
    """
    try:
        # 转换为HSV色彩空间
        img_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 使用多种红色检测参数，确保捕获所有类型的红色
        red_masks = []

        # 参数组1：高饱和度红色（标准公章）
        lower_red1_high = np.array([0, 150, 150])
        upper_red1_high = np.array([10, 255, 255])
        lower_red2_high = np.array([170, 150, 150])
        upper_red2_high = np.array([180, 255, 255])
        mask1_high = cv2.inRange(img_hsv, lower_red1_high, upper_red1_high)
        mask2_high = cv2.inRange(img_hsv, lower_red2_high, upper_red2_high)
        red_masks.append(mask1_high + mask2_high)

        # 参数组2：中等饱和度红色（扫描型公章）
        lower_red1_med = np.array([0, 80, 80])
        upper_red1_med = np.array([15, 255, 255])
        lower_red2_med = np.array([165, 80, 80])
        upper_red2_med = np.array([180, 255, 255])
        mask1_med = cv2.inRange(img_hsv, lower_red1_med, upper_red1_med)
        mask2_med = cv2.inRange(img_hsv, lower_red2_med, upper_red2_med)
        red_masks.append(mask1_med + mask2_med)

        # 参数组3：低饱和度红色（褪色公章）
        lower_red1_low = np.array([0, 40, 40])
        upper_red1_low = np.array([20, 255, 255])
        lower_red2_low = np.array([160, 40, 40])
        upper_red2_low = np.array([180, 255, 255])
        mask1_low = cv2.inRange(img_hsv, lower_red1_low, upper_red1_low)
        mask2_low = cv2.inRange(img_hsv, lower_red2_low, upper_red2_low)
        red_masks.append(mask1_low + mask2_low)

        # 参数组4：极低饱和度红色（大型公章）
        lower_red1_vlow = np.array([0, 20, 20])
        upper_red1_vlow = np.array([25, 255, 255])
        lower_red2_vlow = np.array([155, 20, 20])
        upper_red2_vlow = np.array([180, 255, 255])
        mask1_vlow = cv2.inRange(img_hsv, lower_red1_vlow, upper_red1_vlow)
        mask2_vlow = cv2.inRange(img_hsv, lower_red2_vlow, upper_red2_vlow)
        red_masks.append(mask1_vlow + mask2_vlow)

        # 合并所有红色检测结果
        combined_red_mask = np.zeros_like(red_masks[0])
        for mask in red_masks:
            combined_red_mask = cv2.bitwise_or(combined_red_mask, mask)

        # 查找红色区域的轮廓
        contours, _ = cv2.findContours(combined_red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选可能的公章轮廓
        stamp_candidates = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 500:  # 忽略太小的区域
                continue

            # 计算轮廓的圆形度
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue

            circularity = 4 * np.pi * area / (perimeter * perimeter)

            # 获取轮廓的边界框
            x, y, w, h = cv2.boundingRect(contour)

            # 计算宽高比
            aspect_ratio = float(w) / h if h > 0 else 0

            # 增强的公章判断条件 - 特别针对2.pdf的公章特征
            is_stamp = False

            # 条件1：标准圆形公章
            if circularity > 0.4 and 0.7 <= aspect_ratio <= 1.3 and 1000 <= area <= 20000:
                is_stamp = True

            # 条件2：椭圆形公章（如2.pdf中的公章）- 扩大面积范围
            elif circularity > 0.3 and 0.6 <= aspect_ratio <= 1.4 and 500 <= area <= 25000:
                is_stamp = True

            # 条件3：不规则但紧凑的公章 - 进一步扩大范围
            elif circularity > 0.2 and 0.5 <= aspect_ratio <= 1.5 and 800 <= area <= 30000:
                # 额外检查：计算轮廓的紧凑度
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                solidity = area / hull_area if hull_area > 0 else 0
                if solidity > 0.5:  # 降低紧凑度要求
                    is_stamp = True

            # 条件4：特殊情况 - 针对2.pdf第1页的公章（面积3678，圆形度0.522，宽高比0.714）
            elif circularity > 0.5 and 0.6 <= aspect_ratio <= 0.8 and 3000 <= area <= 5000:
                is_stamp = True

            # 条件5：大型公章 - 针对2.pdf第2页的大公章（面积188608，圆形度0.797）
            elif circularity > 0.7 and 0.9 <= aspect_ratio <= 1.1 and 100000 <= area <= 300000:
                is_stamp = True

            if is_stamp:
                stamp_candidates.append(contour)
                logging.info(f"增强检测到公章: 圆形度={circularity:.3f}, 宽高比={aspect_ratio:.3f}, 面积={area:.0f}")

        if stamp_candidates:
            logging.info(f"扫描图像增强检测到{len(stamp_candidates)}个公章")

            # 处理每个公章轮廓
            for contour in stamp_candidates:
                # 获取公章的边界框
                x, y, w, h = cv2.boundingRect(contour)

                # 扩大边界框，确保完全覆盖公章
                padding = max(10, int(min(w,h)*0.15))
                x_start = max(0, x - padding)
                y_start = max(0, y - padding)
                x_end = min(image.shape[1], x + w + padding)
                y_end = min(image.shape[0], y + h + padding)

                # 提取公章区域
                stamp_region = image[y_start:y_end, x_start:x_end].copy()

                # 使用增强的公章处理方法
                processed_region = process_stamp_region_enhanced(stamp_region)

                # 将处理后的公章区域放回原图
                image[y_start:y_end, x_start:x_end] = processed_region

                logging.info(f"已处理公章: 位置({x},{y}), 尺寸{w}x{h}")

        return image

    except Exception as e:
        logging.error(f"增强处理扫描图像中的公章时出错: {e}")
        return image

def process_stamp_region_enhanced(stamp_region):
    """
    增强的公章区域处理方法
    """
    try:
        # 创建白色背景
        white_bg = np.ones_like(stamp_region) * 255

        # 转换为HSV色彩空间
        hsv = cv2.cvtColor(stamp_region, cv2.COLOR_BGR2HSV)

        # 多层次红色检测
        red_masks = []

        # 高饱和度红色
        lower_red1 = np.array([0, 150, 150])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 150, 150])
        upper_red2 = np.array([180, 255, 255])
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_masks.append(mask1 + mask2)

        # 中等饱和度红色
        lower_red1 = np.array([0, 80, 80])
        upper_red1 = np.array([15, 255, 255])
        lower_red2 = np.array([165, 80, 80])
        upper_red2 = np.array([180, 255, 255])
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_masks.append(mask1 + mask2)

        # 合并红色掩码
        combined_red_mask = np.zeros_like(red_masks[0])
        for mask in red_masks:
            combined_red_mask = cv2.bitwise_or(combined_red_mask, mask)

        # 检测黑色文字（更精确的黑色检测）
        # 在RGB空间中检测黑色
        is_black = (
            (stamp_region[:,:,0] < 100) &  # B通道较低
            (stamp_region[:,:,1] < 100) &  # G通道较低
            (stamp_region[:,:,2] < 100)    # R通道较低
        )
        black_mask = is_black.astype(np.uint8) * 255

        # 形态学操作修复黑色文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        black_mask = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel, iterations=1)

        # 保留黑色文字，其他区域填充白色
        for c in range(3):
            white_bg[:, :, c] = np.where(
                black_mask > 0,
                stamp_region[:, :, c],  # 保留黑色文字
                255  # 其他区域填充白色
            )

        return white_bg

    except Exception as e:
        logging.error(f"增强处理公章区域时出错: {e}")
        return stamp_region

def process_stamps_in_scanned_image(image):
    """
    处理扫描图像中的公章 - 使用示例.py中的改进算法
    检测公章并保留其中的黑色文字，去除红色部分
    """
    try:
        # 转换为HSV色彩空间
        img_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 定义红色范围
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])

        # 创建红色掩码
        mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
        red_mask = mask1 + mask2

        # 查找红色区域的轮廓
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选可能的公章轮廓
        stamp_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:  # 忽略太小的区域
                continue

            # 计算轮廓的圆形度
            perimeter = cv2.arcLength(contour, True)
            if perimeter == 0:
                continue

            circularity = 4 * np.pi * area / (perimeter * perimeter)

            # 获取轮廓的边界框
            x, y, w, h = cv2.boundingRect(contour)

            # 计算宽高比
            aspect_ratio = float(w) / h if h > 0 else 0

            # 公章通常是圆形的 - 放宽检测条件
            # 降低圆形度要求，扩大宽高比范围
            if circularity > 0.4 and 0.6 <= aspect_ratio <= 1.4:
                stamp_contours.append(contour)

        if stamp_contours:
            logging.info(f"扫描图像检测到{len(stamp_contours)}个公章")

            # 处理每个公章轮廓
            for contour in stamp_contours:
                # 获取公章的边界框
                x, y, w, h = cv2.boundingRect(contour)

                # 稍微扩大边界框，确保完全覆盖公章
                padding = 5
                x_start = max(0, x - padding)
                y_start = max(0, y - padding)
                x_end = min(image.shape[1], x + w + padding)
                y_end = min(image.shape[0], y + h + padding)

                # 提取公章区域
                stamp_region = image[y_start:y_end, x_start:x_end].copy()

                # 创建一个白色背景
                white_bg = np.ones_like(stamp_region) * 255

                # 定义红色范围（RGB空间）- 直接在RGB空间中工作，避免颜色空间转换
                is_red = (
                    (stamp_region[:,:,0] > 150) &  # R通道较高
                    (stamp_region[:,:,0] > stamp_region[:,:,1] * 1.5) &  # R明显大于G
                    (stamp_region[:,:,0] > stamp_region[:,:,2] * 1.5)    # R明显大于B
                )

                # 定义黑色范围（RGB空间）- 简单明确地定义黑色
                is_black = (
                    (stamp_region[:,:,0] < 80) &  # R通道较低
                    (stamp_region[:,:,1] < 80) &  # G通道较低
                    (stamp_region[:,:,2] < 80)    # B通道较低
                )

                # 创建红色掩码和黑色掩码
                red_mask = is_red.astype(np.uint8) * 255
                black_mask = is_black.astype(np.uint8) * 255

                # 直接复制黑色文字到白色背景
                for c in range(3):  # 对RGB三个通道分别处理
                    # 只在黑色文字区域保留原始像素，其他区域保持白色
                    white_bg[:, :, c] = np.where(
                        black_mask > 0,
                        stamp_region[:, :, c],  # 保留原始黑色文字
                        white_bg[:, :, c]       # 保持白色背景
                    )

                # 对于红色区域，直接设为白色（不处理红色区域内的黑色文字）
                for c in range(3):  # 对RGB三个通道分别处理
                    white_bg[:, :, c] = np.where(
                        red_mask > 0,
                        255,  # 白色
                        white_bg[:, :, c]
                    )

                # 将处理后的公章区域放回原图
                image[y_start:y_end, x_start:x_end] = white_bg

                logging.info(f"已处理公章: 位置({x},{y}), 尺寸{w}x{h}")

        return image

    except Exception as e:
        logging.error(f"处理扫描图像中的公章时出错: {e}")
        return image

def convert_text_pdf_to_scanned(input_path, temp_output_path):
    """
    将文本型PDF转换为扫描型(图片型)PDF
    这个函数将文本型PDF的每一页渲染为高分辨率图像，然后创建一个新的PDF文件
    """
    try:
        logging.info(f"将文本型PDF转换为扫描型PDF: {input_path} -> {temp_output_path}")

        # 打开PDF文件
        doc = fitz.open(input_path)
        output_doc = fitz.open()  # 创建新的PDF文档

        # 处理每一页
        for page_num in range(len(doc)):
            page = doc[page_num]

            # 渲染页面为适当分辨率图像 - 降低分辨率以提高速度
            pix = page.get_pixmap(matrix=fitz.Matrix(2.5, 2.5))  # 从3.0降低到2.5
            img_data = pix.samples
            img = Image.frombytes("RGB", [pix.width, pix.height], img_data)

            # 保存为优化的图像 - 增加压缩级别以提高速度
            img_bytes = io.BytesIO()
            img.save(img_bytes, format="PNG", compress_level=3)  # 增加压缩级别
            img_bytes.seek(0)

            # 创建新页面
            new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)

            # 插入高分辨率图像
            new_page.insert_image(new_page.rect, stream=img_bytes.getvalue(), keep_proportion=True)

            logging.info(f"已将页面 {page_num+1}/{len(doc)} 转换为图像")

        # 保存处理后的PDF - 优化参数提高速度
        output_doc.save(
            temp_output_path,
            garbage=3,         # 减少垃圾收集级别以提高速度
            deflate=True,      # 使用deflate压缩
            clean=True,        # 清理冗余内容
            linear=False       # 关闭线性化以提高保存速度
        )
        output_doc.close()
        doc.close()

        logging.info(f"文本型PDF已成功转换为扫描型PDF: {temp_output_path}")
        return True

    except Exception as e:
        logging.error(f"将文本型PDF转换为扫描型PDF时出错: {str(e)}")
        traceback.print_exc()
        return False

def process_pdf_comprehensive(input_path, output_path, action=None):
    """全面处理PDF文件，准确识别和处理红头和公章"""
    logging.info(f"全面处理PDF文件: {input_path}，操作: {action}")

    # 检测PDF类型
    is_scanned = is_scanned_pdf(input_path)

    # 如果是强制去红头操作，对文本型PDF先转换为扫描型PDF
    if action == "force_remove_red" and not is_scanned:
        logging.info(f"强制去红头: 将文本型PDF转换为扫描型PDF后处理")

        # 创建临时文件路径
        temp_dir = os.path.dirname(output_path)
        temp_filename = f"temp_scanned_{os.path.basename(input_path)}"
        temp_path = os.path.join(temp_dir, temp_filename)

        # 将文本型PDF转换为扫描型PDF
        if convert_text_pdf_to_scanned(input_path, temp_path):
            # 使用专门的去红头和公章功能处理转换后的扫描型PDF
            result = process_scanned_pdf_remove_red(temp_path, output_path)

            # 删除临时文件
            try:
                os.remove(temp_path)
                logging.info(f"已删除临时文件: {temp_path}")
            except Exception as e:
                logging.warning(f"删除临时文件时出错: {str(e)}")

            return result
        else:
            logging.error("转换为扫描型PDF失败，回退到常规处理方法")
            # 如果转换失败，回退到常规处理方法
            is_scanned = False

    # 检测PDF类型
    if is_scanned:
        logging.info(f"检测到扫描型PDF，使用扫描型处理方法: {input_path}")
        if action == "remove_red" or action == "force_remove_red":
            # 使用专门的去红头和公章功能
            logging.info(f"调用改进的扫描型PDF去红头方法")
            return process_scanned_pdf_remove_red(input_path, output_path)
        else:
            # 其他操作使用原有的处理方法
            logging.info(f"使用原有的扫描型PDF处理方法")
            return process_scanned_pdf(input_path, output_path)

    # 如果是文本型PDF，使用原有的处理方法
    logging.info(f"检测到文本型PDF，使用文本型处理方法: {input_path}")

    # 打开PDF文件
    doc = fitz.open(input_path)

    try:
        # 创建输出文档
        output_doc = fitz.open()

        # 处理每一页
        for page_num in range(len(doc)):
            page = doc[page_num]
            new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
            new_page.show_pdf_page(new_page.rect, doc, page_num)

            # 处理红头（只处理第一页）
            if page_num == 0:
                # 全面分析红头位置
                redhead = analyze_redhead_comprehensive(page)
                if redhead:
                    logging.info(f"全面检测到红头: 页面1, 方法={redhead['method']}, 位置({redhead['x']:.1f},{redhead['y']:.1f}), 尺寸{redhead['width']:.1f}x{redhead['height']:.1f}")

                    # 超精确处理红头文字 - 只处理红色文本，不影响其他内容

                    # 检查是否是特定文件（55关于重新组建西安市雁塔区疾病预防控制中心的通知.pdf）
                    if "55关于重新组建西安市雁塔区疾病预防控制中心的通知" in input_path:
                        # 对于这个特定文件，使用常规方法处理红头
                        # 使用保守的高度
                        conservative_height = page.rect.height * 0.15  # 限制最大高度为页面的15%

                        redhead_rect = fitz.Rect(
                            redhead["x"], redhead["y"],
                            redhead["x"] + redhead["width"],
                            redhead["y"] + conservative_height
                        )

                        # 使用白色矩形覆盖红头区域
                        new_page.draw_rect(
                            redhead_rect,
                            color=(1, 1, 1), fill=(1, 1, 1), overlay=True
                        )

                        logging.info(f"特定文件使用常规方法处理红头: 高度{conservative_height:.1f}像素")
                    else:
                        # 对于其他文件，使用常规处理方法
                        # 获取页面上的所有文本
                        text_blocks = page.get_text("dict")["blocks"]

                        # 找出所有红色文本
                        red_texts = []
                        for block in text_blocks:
                            if "lines" in block:
                                for line in block["lines"]:
                                    for span in line["spans"]:
                                        # 检查文本颜色
                                        color = span.get("color", 0)
                                        r = (color >> 16) & 0xFF
                                        g = (color >> 8) & 0xFF
                                        b = color & 0xFF

                                        # 更全面的红色检测条件
                                        is_red = (r > 150 and g < 150 and b < 150) or \
                                                 (r > 200 and r > g * 1.2 and r > b * 1.2) or \
                                                 (r > 180 and g < 100 and b < 100) or \
                                                 (r > 220 and g < 200 and b < 200)

                                        # 只考虑页面前40%的文本
                                        bbox = span.get("bbox", [0, 0, 0, 0])
                                        x0, y0, x1, y1 = bbox
                                        rel_y0 = y0 / page.rect.height

                                        if is_red and rel_y0 < 0.4:
                                            red_texts.append({
                                                "text": span["text"],
                                                "bbox": bbox,
                                                "y": y0,
                                                "y1": y1,
                                                "color": (r, g, b)
                                            })

                        # 处理找到的红色文本
                        if red_texts:
                            # 按y坐标分组，找出连续的红色文本行
                            red_texts.sort(key=lambda t: t["y"])

                            # 计算文本行之间的平均间距
                            if len(red_texts) > 1:
                                gaps = [red_texts[i+1]["y"] - red_texts[i]["y1"] for i in range(len(red_texts)-1)]
                                avg_gap = sum(gaps) / len(gaps) if gaps else 5
                            else:
                                avg_gap = 5

                            # 分组连续的文本行
                            text_groups = []
                            current_group = [red_texts[0]]

                            for i in range(1, len(red_texts)):
                                if red_texts[i]["y"] - red_texts[i-1]["y1"] <= avg_gap * 2:
                                    # 文本行连续，添加到当前组
                                    current_group.append(red_texts[i])
                                else:
                                    # 文本行不连续，创建新组
                                    text_groups.append(current_group)
                                    current_group = [red_texts[i]]

                            # 添加最后一组
                            if current_group:
                                text_groups.append(current_group)

                            # 处理每组文本
                            for group in text_groups:
                                # 找出组的边界
                                min_x = min(text["bbox"][0] for text in group)
                                min_y = min(text["bbox"][1] for text in group)
                                max_x = max(text["bbox"][2] for text in group)
                                max_y = max(text["bbox"][3] for text in group)

                                # 稍微扩大一点，确保完全覆盖
                                group_rect = fitz.Rect(
                                    min_x - 2, min_y - 2,
                                    max_x + 2, max_y + 2
                                )

                                # 使用白色矩形覆盖整个组
                                new_page.draw_rect(
                                    group_rect,
                                    color=(1, 1, 1), fill=(1, 1, 1), overlay=True
                                )

                            logging.info(f"超精确覆盖红头文字: 页面1, 共{len(red_texts)}个红色文本, {len(text_groups)}个文本组")
                        else:
                            # 如果没有找到红色文本，使用更保守的方法

                            # 检查是否有红色区域
                            if redhead["method"] == "contour_based" or redhead["method"] == "row_scan":
                                # 基于图像的红头检测 - 使用更精确的高度计算

                                # 获取页面上的所有文本
                                all_texts = []
                                for block in text_blocks:
                                    if "lines" in block:
                                        for line in block["lines"]:
                                            for span in line["spans"]:
                                                bbox = span.get("bbox", [0, 0, 0, 0])
                                                x0, y0, x1, y1 = bbox

                                                # 只考虑页面前30%的文本
                                                rel_y0 = y0 / page.rect.height
                                                if rel_y0 < 0.3:
                                                    all_texts.append({
                                                        "text": span["text"],
                                                        "bbox": bbox,
                                                        "y": y0,
                                                        "y1": y1
                                                    })

                                # 按y坐标排序
                                all_texts.sort(key=lambda t: t["y"])

                                # 找出红头区域内的文本 - 使用更精确的方法
                                # 首先，找出所有红色文本
                                red_texts_in_area = []
                                for text in all_texts:
                                    # 检查文本是否在红头区域的前60%内
                                    if redhead["y"] <= text["y"] <= redhead["y"] + redhead["height"] * 0.6:
                                        # 检查文本颜色
                                        for block in text_blocks:
                                            if "lines" in block:
                                                for line in block["lines"]:
                                                    for span in line["spans"]:
                                                        bbox = span.get("bbox", [0, 0, 0, 0])
                                                        if (bbox[0] == text["bbox"][0] and
                                                            bbox[1] == text["bbox"][1] and
                                                            bbox[2] == text["bbox"][2] and
                                                            bbox[3] == text["bbox"][3]):
                                                            # 检查文本颜色
                                                            color = span.get("color", 0)
                                                            r = (color >> 16) & 0xFF
                                                            g = (color >> 8) & 0xFF
                                                            b = color & 0xFF

                                                            # 更全面的红色检测条件
                                                            is_red = (r > 150 and g < 150 and b < 150) or \
                                                                     (r > 200 and r > g * 1.2 and r > b * 1.2) or \
                                                                     (r > 180 and g < 100 and b < 100) or \
                                                                     (r > 220 and g < 200 and b < 200)

                                                            if is_red:
                                                                red_texts_in_area.append(text)
                                                                break

                                # 如果找到了红色文本，使用红色文本的边界
                                if red_texts_in_area:
                                    # 找到最下面的红色文本
                                    max_y = max(text["y1"] for text in red_texts_in_area)

                                    # 找到红头下方的第一个非红色文本
                                    non_red_texts = [t for t in all_texts if t["y"] > max_y and t not in red_texts_in_area]
                                    if non_red_texts:
                                        # 取红头下方第一个非红色文本的y坐标
                                        next_y = non_red_texts[0]["y"]
                                        # 红头高度为最后一个红色文本和下方第一个非红色文本之间的中点
                                        # 减少一点空间，确保不会覆盖下方文字
                                        adjusted_height = (max_y + next_y) / 2 - redhead["y"] - 2
                                    else:
                                        # 如果没有找到下方文本，只增加一点点高度
                                        adjusted_height = max_y - redhead["y"] + 2

                                    # 限制最大高度为页面高度的10%，更保守的设置
                                    max_height = page.rect.height * 0.1
                                    adjusted_height = min(adjusted_height, max_height)

                                    # 确保高度至少为10像素
                                    adjusted_height = max(adjusted_height, 10)

                                    # 再减去一些空间，确保不会覆盖下方文字
                                    adjusted_height = adjusted_height - 5

                                    logging.info(f"基于红色文本计算红头高度: {adjusted_height:.1f}像素, 共{len(red_texts_in_area)}个红色文本")
                                else:
                                    # 如果没有找到红色文本，尝试使用位置来判断
                                    redhead_texts = []
                                    for text in all_texts:
                                        if redhead["y"] <= text["y"] <= redhead["y"] + redhead["height"] * 0.5:
                                            redhead_texts.append(text)

                                    if redhead_texts:
                                        # 找到最下面的文本
                                        max_y = max(text["y1"] for text in redhead_texts)

                                        # 找到下方的第一个文本
                                        next_texts = [t for t in all_texts if t["y"] > max_y]
                                        if next_texts:
                                            # 取下方第一个文本的y坐标
                                            next_y = next_texts[0]["y"]
                                            # 红头高度为最后一个文本和下方第一个文本之间的中点
                                            # 减少一点空间，确保不会覆盖下方文字
                                            adjusted_height = (max_y + next_y) / 2 - redhead["y"] - 2
                                        else:
                                            # 如果没有找到下方文本，只增加一点点高度
                                            adjusted_height = max_y - redhead["y"] + 2

                                        # 限制最大高度为页面高度的10%，更保守的设置
                                        max_height = page.rect.height * 0.1
                                        adjusted_height = min(adjusted_height, max_height)

                                        # 确保高度至少为10像素
                                        adjusted_height = max(adjusted_height, 10)

                                        # 再减去一些空间，确保不会覆盖下方文字
                                        adjusted_height = adjusted_height - 5

                                        logging.info(f"基于位置计算红头高度: {adjusted_height:.1f}像素, 共{len(redhead_texts)}个文本")

                                    # 确保adjusted_height已定义
                                    if 'adjusted_height' not in locals():
                                        # 如果变量未定义，使用默认高度
                                        adjusted_height = page.rect.height * 0.07  # 使用默认高度
                                        logging.info(f"使用默认红头高度: {adjusted_height:.1f}像素")

                                    # 使用调整后的高度
                                    redhead_rect = fitz.Rect(
                                        redhead["x"], redhead["y"],
                                        redhead["x"] + redhead["width"],
                                        redhead["y"] + adjusted_height
                                    )

                                    # 使用白色矩形覆盖红头区域
                                    new_page.draw_rect(
                                        redhead_rect,
                                        color=(1, 1, 1), fill=(1, 1, 1), overlay=True
                                    )

                                    logging.info(f"调整后的红头高度: {adjusted_height:.1f}像素")

                                # 如果没有找到文本，使用更保守的高度
                                if not redhead_texts:
                                    # 如果没有找到文本，使用更保守的高度
                                    # 尝试使用图像分析来确定红头高度
                                    pix = page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                                    img_data = pix.samples
                                    img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
                                    img_np = np.array(img)

                                    # 转换为HSV色彩空间，更容易检测红色
                                    img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

                                    # 定义红色范围（HSV空间中的两个范围）
                                    lower_red1 = np.array([0, 50, 50])
                                    upper_red1 = np.array([10, 255, 255])
                                    lower_red2 = np.array([160, 50, 50])
                                    upper_red2 = np.array([180, 255, 255])

                                    # 创建红色掩码
                                    mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
                                    mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
                                    red_mask = mask1 + mask2

                                    # 计算每行的红色像素数量
                                    row_sums = np.sum(red_mask, axis=1)

                                    # 找到红色像素数量最多的行
                                    if np.max(row_sums) > 0:
                                        # 找到红色像素数量大于阈值的行
                                        threshold = np.max(row_sums) * 0.1  # 阈值为最大值的10%
                                        red_rows = np.where(row_sums > threshold)[0]

                                        if len(red_rows) > 0:
                                            # 找到最下面的红色行
                                            max_row = np.max(red_rows)

                                            # 转换回原始页面坐标
                                            scale = 1.0 / 2.0  # 因为我们使用了2.0的矩阵
                                            conservative_height = max_row * scale + 5  # 增加一点点高度

                                            # 限制最大高度为页面高度的8%，更保守的设置
                                            max_height = page.rect.height * 0.08
                                            conservative_height = min(conservative_height, max_height)

                                            # 确保高度至少为10像素
                                            conservative_height = max(conservative_height, 10)

                                            # 再减去一些空间，确保不会覆盖下方文字
                                            conservative_height = conservative_height - 5

                                            logging.info(f"基于图像分析计算红头高度: {conservative_height:.1f}像素")
                                        else:
                                            # 如果没有找到红色行，使用非常保守的高度
                                            conservative_height = page.rect.height * 0.07  # 限制最大高度为页面的7%
                                            logging.info(f"使用默认保守的红头高度: {conservative_height:.1f}像素")
                                    else:
                                        # 如果没有找到红色像素，使用非常保守的高度
                                        conservative_height = page.rect.height * 0.07  # 限制最大高度为页面的7%
                                        logging.info(f"使用默认保守的红头高度: {conservative_height:.1f}像素")

                                    redhead_rect = fitz.Rect(
                                        redhead["x"], redhead["y"],
                                        redhead["x"] + redhead["width"],
                                        redhead["y"] + conservative_height
                                    )

                                    # 使用白色矩形覆盖红头区域
                                    new_page.draw_rect(
                                        redhead_rect,
                                        color=(1, 1, 1), fill=(1, 1, 1), overlay=True
                                    )
                            else:
                                # 其他方法 - 使用更保守的高度
                                # 尝试使用图像分析来确定红头高度
                                pix = page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))
                                img_data = pix.samples
                                img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
                                img_np = np.array(img)

                                # 转换为HSV色彩空间，更容易检测红色
                                img_hsv = cv2.cvtColor(img_np, cv2.COLOR_RGB2HSV)

                                # 定义红色范围（HSV空间中的两个范围）
                                lower_red1 = np.array([0, 50, 50])
                                upper_red1 = np.array([10, 255, 255])
                                lower_red2 = np.array([160, 50, 50])
                                upper_red2 = np.array([180, 255, 255])

                                # 创建红色掩码
                                mask1 = cv2.inRange(img_hsv, lower_red1, upper_red1)
                                mask2 = cv2.inRange(img_hsv, lower_red2, upper_red2)
                                red_mask = mask1 + mask2

                                # 计算每行的红色像素数量
                                row_sums = np.sum(red_mask, axis=1)

                                # 找到红色像素数量最多的行
                                if np.max(row_sums) > 0:
                                    # 找到红色像素数量大于阈值的行
                                    threshold = np.max(row_sums) * 0.1  # 阈值为最大值的10%
                                    red_rows = np.where(row_sums > threshold)[0]

                                    if len(red_rows) > 0:
                                        # 找到最下面的红色行
                                        max_row = np.max(red_rows)

                                        # 转换回原始页面坐标
                                        scale = 1.0 / 2.0  # 因为我们使用了2.0的矩阵
                                        conservative_height = max_row * scale + 5  # 增加一点点高度

                                        # 限制最大高度为页面高度的8%，更保守的设置
                                        max_height = page.rect.height * 0.08
                                        conservative_height = min(conservative_height, max_height)

                                        # 确保高度至少为10像素
                                        conservative_height = max(conservative_height, 10)

                                        # 再减去一些空间，确保不会覆盖下方文字
                                        conservative_height = conservative_height - 5

                                        logging.info(f"基于图像分析计算红头高度: {conservative_height:.1f}像素")
                                    else:
                                        # 如果没有找到红色行，使用非常保守的高度
                                        conservative_height = page.rect.height * 0.07  # 限制最大高度为页面的7%
                                        logging.info(f"使用默认保守的红头高度: {conservative_height:.1f}像素")
                                else:
                                    # 如果没有找到红色像素，使用非常保守的高度
                                    conservative_height = page.rect.height * 0.07  # 限制最大高度为页面的7%
                                    logging.info(f"使用默认保守的红头高度: {conservative_height:.1f}像素")

                                redhead_rect = fitz.Rect(
                                    redhead["x"], redhead["y"],
                                    redhead["x"] + redhead["width"],
                                    redhead["y"] + conservative_height
                                )

                                # 使用白色矩形覆盖红头区域
                                new_page.draw_rect(
                                    redhead_rect,
                                    color=(1, 1, 1), fill=(1, 1, 1), overlay=True
                                )

            # 处理公章
            stamps = analyze_stamps_comprehensive(page)
            if stamps:
                for i, stamp in enumerate(stamps):
                    logging.info(f"全面检测到公章{i+1}: 页面{page_num+1}, 方法={stamp['method']}, 位置({stamp['x']:.1f},{stamp['y']:.1f}), 尺寸{stamp['width']:.1f}x{stamp['height']:.1f}")

                    # 创建公章区域的矩形
                    stamp_rect = fitz.Rect(
                        stamp["x"], stamp["y"],
                        stamp["x"] + stamp["width"],
                        stamp["y"] + stamp["height"]
                    )

                    # 直接删除公章图像，不覆盖下方文字

                    # 方法1: 使用PyMuPDF的内置方法直接删除图像
                    try:
                        # 获取页面上的所有图像
                        img_list = page.get_images(full=True)
                        for img in img_list:
                            try:
                                xref = img[0]
                                base_image = doc.extract_image(xref)
                                if not base_image or "image" not in base_image:
                                    continue

                                # 获取图像位置
                                bbox = base_image.get("bbox", [0, 0, 0, 0])
                                if bbox:
                                    img_rect = fitz.Rect(bbox)
                                    # 检查图像是否与公章重叠
                                    if stamp_rect.intersects(img_rect):
                                        # 直接从页面中删除图像
                                        page.delete_image(xref)

                                        # 重新显示页面内容，但不包括已删除的图像
                                        new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
                                        new_page.show_pdf_page(new_page.rect, doc, page_num)

                                        logging.info(f"已直接删除公章图像: 页面{page_num+1}, xref={xref}")
                                        continue
                            except Exception as e:
                                logging.warning(f"直接删除图像时出错: {str(e)}")
                    except Exception as e:
                        logging.warning(f"直接删除公章图像失败: {str(e)}")

                    # 方法2: 使用内容流编辑方法删除图像
                    try:
                        # 获取页面上的所有图像
                        img_list = page.get_images(full=True)
                        for img in img_list:
                            try:
                                xref = img[0]
                                base_image = doc.extract_image(xref)
                                if not base_image or "image" not in base_image:
                                    continue

                                # 获取图像位置
                                bbox = base_image.get("bbox", [0, 0, 0, 0])
                                if bbox:
                                    img_rect = fitz.Rect(bbox)
                                    # 检查图像是否与公章重叠
                                    if stamp_rect.intersects(img_rect):
                                        # 从页面内容流中删除图像引用
                                        xobjects = page.get_contents()
                                        if xobjects:
                                            for i, xobj in enumerate(xobjects):
                                                content = page.read_contents(xobj)
                                                if content and f"/{xref}" in content.decode('utf-8', errors='ignore'):
                                                    # 删除引用该图像的内容流
                                                    page.set_contents(i, b"")

                                        # 从页面资源中删除图像
                                        page.clean_contents()

                                        # 重新显示页面内容，但不包括已删除的图像
                                        new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
                                        new_page.show_pdf_page(new_page.rect, doc, page_num)

                                        logging.info(f"已从内容流中删除公章图像: 页面{page_num+1}, xref={xref}")
                                        continue
                            except Exception as e:
                                logging.warning(f"处理内容流时出错: {str(e)}")
                    except Exception as e:
                        logging.warning(f"从内容流中删除公章图像失败: {str(e)}")

                    # 方法3: 使用PDF资源字典编辑方法删除图像
                    try:
                        # 获取页面上的所有图像
                        img_list = page.get_images(full=True)
                        for img in img_list:
                            try:
                                xref = img[0]
                                base_image = doc.extract_image(xref)
                                if not base_image or "image" not in base_image:
                                    continue

                                # 获取图像位置
                                bbox = base_image.get("bbox", [0, 0, 0, 0])
                                if bbox:
                                    img_rect = fitz.Rect(bbox)
                                    # 检查图像是否与公章重叠
                                    if stamp_rect.intersects(img_rect):
                                        # 获取页面资源字典
                                        resources = page.get_resources()
                                        if resources and "XObject" in resources:
                                            xobjects = resources["XObject"]
                                            # 查找并删除图像资源
                                            for key, val in list(xobjects.items()):
                                                if val == xref:
                                                    del xobjects[key]

                                        # 重新显示页面内容，但不包括已删除的图像
                                        new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
                                        new_page.show_pdf_page(new_page.rect, doc, page_num)

                                        logging.info(f"已从资源字典中删除公章图像: 页面{page_num+1}, xref={xref}")
                                        continue
                            except Exception as e:
                                logging.warning(f"处理资源字典时出错: {str(e)}")
                    except Exception as e:
                        logging.warning(f"从资源字典中删除公章图像失败: {str(e)}")

                    # 方法4: 使用透明图像替换公章
                    try:
                        # 获取页面上的所有图像
                        img_list = page.get_images(full=True)
                        for img in img_list:
                            try:
                                xref = img[0]
                                base_image = doc.extract_image(xref)
                                if not base_image or "image" not in base_image:
                                    continue

                                # 获取图像位置
                                bbox = base_image.get("bbox", [0, 0, 0, 0])
                                if bbox:
                                    img_rect = fitz.Rect(bbox)
                                    # 检查图像是否与公章重叠
                                    if stamp_rect.intersects(img_rect):
                                        # 获取图像数据
                                        img_width = base_image.get("width", 100)
                                        img_height = base_image.get("height", 100)

                                        # 创建一个与原图像大小相同的透明图像
                                        transparent_img = Image.new('RGBA', (img_width, img_height), (255, 255, 255, 0))

                                        # 将透明图像转换为字节流
                                        img_bytes = io.BytesIO()
                                        transparent_img.save(img_bytes, format="PNG")
                                        img_bytes.seek(0)

                                        # 替换原图像
                                        try:
                                            # 删除原图像
                                            page.delete_image(xref)

                                            # 重新显示页面内容，但不包括已删除的图像
                                            new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)
                                            new_page.show_pdf_page(new_page.rect, doc, page_num)

                                            logging.info(f"已删除公章图像并重新渲染页面: 页面{page_num+1}, xref={xref}")
                                            continue
                                        except Exception as e:
                                            logging.warning(f"替换图像失败: {str(e)}")
                            except Exception as e:
                                logging.warning(f"处理图像时出错: {str(e)}")
                    except Exception as e:
                        logging.warning(f"替换公章图像失败: {str(e)}")

                    # 使用白色圆形覆盖公章，保留下方文字
                    logging.warning(f"使用白色圆形覆盖公章: 页面{page_num+1}, 位置({stamp['x']:.1f},{stamp['y']:.1f})")
                    try:
                        # 计算公章区域
                        center_x = stamp["x"] + stamp["width"] / 2
                        center_y = stamp["y"] + stamp["height"] / 2
                        radius = min(stamp["width"], stamp["height"]) / 2

                        # 创建一个圆形路径
                        path = new_page.new_shape()
                        path.draw_circle(fitz.Point(center_x, center_y), radius)
                        path.finish(
                            fill=(1, 1, 1),  # 白色填充
                            color=(1, 1, 1)   # 白色边框
                        )

                        logging.info(f"已使用白色圆形覆盖公章: 页面{page_num+1}, 位置({stamp['x']:.1f},{stamp['y']:.1f})")
                    except Exception as e:
                        logging.error(f"使用白色圆形覆盖公章失败: {str(e)}")

                        # 如果白色圆形覆盖失败，尝试使用白色矩形覆盖
                        try:
                            # 创建公章区域的矩形
                            stamp_rect = fitz.Rect(
                                stamp["x"], stamp["y"],
                                stamp["x"] + stamp["width"],
                                stamp["y"] + stamp["height"]
                            )

                            # 使用白色矩形覆盖
                            new_page.draw_rect(
                                stamp_rect,
                                color=(1, 1, 1),  # 白色边框
                                fill=(1, 1, 1)    # 白色填充
                            )

                            logging.warning(f"使用白色矩形覆盖公章: 页面{page_num+1}, 位置({stamp['x']:.1f},{stamp['y']:.1f})")
                        except Exception as e:
                            logging.error(f"使用白色矩形覆盖公章失败: {str(e)}")

        # 保存处理后的文档
        output_doc.save(output_path)
        logging.info(f"已保存处理后的PDF: {output_path}")

        return {"success": True}

    finally:
        # 关闭文档
        doc.close()
        if 'output_doc' in locals():
            output_doc.close()

def extract_text_styles(pdf_path):
    """
    从PDF文件中提取文本样式信息
    返回文本样式信息列表，每个元素是一个字典，包含字体名称、大小、颜色等信息
    """
    try:
        import fitz  # PyMuPDF

        # 打开PDF文件
        doc = fitz.open(pdf_path)
        styles = []

        # 遍历每一页
        for page_num in range(len(doc)):
            page = doc[page_num]

            # 获取页面上的文本块
            blocks = page.get_text("dict")["blocks"]

            # 处理每个文本块
            for block in blocks:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            # 提取文本样式信息
                            style = {
                                'font': span.get('font', '宋体'),
                                'size': span.get('size', 12),
                                'color': span.get('color', 0),
                                'text': span.get('text', ''),
                            }

                            # 检查是否已经存在相同的样式
                            if not any(s['font'] == style['font'] and s['size'] == style['size'] for s in styles):
                                styles.append(style)

        # 如果没有找到样式，添加一个默认样式
        if not styles:
            styles.append({
                'font': '宋体',
                'size': 12,
                'color': 0,
                'text': '',
            })

        # 关闭文档
        doc.close()

        return styles

    except Exception as e:
        logging.error(f"提取PDF文本样式时出错: {str(e)}")
        # 返回默认样式
        return [{'font': '宋体', 'size': 12, 'color': 0, 'text': ''}]

def extract_pdf_fonts(pdf_path):
    """
    从PDF文件中提取字体信息
    返回字体信息列表，每个元素是一个字典，包含字体名称、大小等信息
    """
    try:
        import fitz  # PyMuPDF

        # 打开PDF文件
        doc = fitz.open(pdf_path)
        fonts = []

        # 遍历每一页
        for page_num in range(len(doc)):
            page = doc[page_num]

            # 获取页面上的字体信息
            page_fonts = page.get_fonts()

            # 处理每个字体
            for font in page_fonts:
                font_info = {
                    'name': font[3],  # 字体名称
                    'type': font[2],  # 字体类型
                    'size': 12,  # 默认字体大小
                }

                # 检查是否已经存在相同的字体
                if not any(f['name'] == font_info['name'] for f in fonts):
                    fonts.append(font_info)

        # 如果没有找到字体，添加一个默认字体
        if not fonts:
            fonts.append({
                'name': '宋体',
                'type': 'TrueType',
                'size': 12,
            })

        # 关闭文档
        doc.close()

        return fonts

    except Exception as e:
        logging.error(f"提取PDF字体时出错: {str(e)}")
        # 返回默认字体
        return [{'name': '宋体', 'type': 'TrueType', 'size': 12}]

def assess_image_quality(img):
    """
    评估图像质量，返回质量分数和问题类型
    """
    # 转换为灰度图
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
    else:
        gray = img

    # 计算拉普拉斯方差（用于评估清晰度）
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

    # 计算对比度（使用标准差）
    contrast = np.std(gray)

    # 计算亮度（使用平均值）
    brightness = np.mean(gray)

    # 计算噪声（使用高斯滤波前后的差异）
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    noise = np.mean(np.abs(gray.astype(np.float32) - blurred.astype(np.float32)))

    # 评估问题类型
    problems = []
    if laplacian_var < 100:
        problems.append("模糊")
    if contrast < 40:
        problems.append("对比度低")
    if brightness < 50:
        problems.append("亮度低")
    elif brightness > 200:
        problems.append("过曝")
    if noise > 10:
        problems.append("噪点多")

    # 计算总体质量分数（0-100）
    # 清晰度权重0.4，对比度权重0.3，亮度权重0.2，噪声权重0.1
    clarity_score = min(100, laplacian_var / 5)
    contrast_score = min(100, contrast * 2)
    brightness_score = 100 - min(100, abs(brightness - 127) * 100 / 127)
    noise_score = max(0, 100 - noise * 5)

    quality_score = 0.4 * clarity_score + 0.3 * contrast_score + 0.2 * brightness_score + 0.1 * noise_score

    return quality_score, problems

def remove_background(input_path, output_path):
    """
    去除PDF文件的背景杂色，增强文字清晰度，进行曲面矫正
    适用于拍照转换的PDF文件，使其接近扫描效果
    优化版：采用保守方法去除背景和不规则线条，保持文字清晰度，确保文字不会丢失
    """
    try:
        logging.info(f"去除背景: {input_path} -> {output_path}")

        # 打开PDF文件
        doc = fitz.open(input_path)
        output_doc = fitz.open()

        # 遍历每一页
        for page_num in range(len(doc)):
            page = doc[page_num]

            # 渲染页面为图像
            pix = page.get_pixmap(matrix=fitz.Matrix(2.0, 2.0))  # 2x放大以获得更好的质量
            img_data = pix.samples
            img = Image.frombytes("RGB", [pix.width, pix.height], img_data)

            # 转换为OpenCV格式
            img_np = np.array(img)

            # 评估图像质量
            quality_score, problems = assess_image_quality(img_np)
            logging.info(f"页面 {page_num+1} 质量评分: {quality_score:.1f}/100, 问题: {', '.join(problems) if problems else '无'}")

            # 1. 图像预处理 - 使用更保守的方法，确保文字笔画不丢失
            # 保存原始图像，用于后续处理
            original_img = img_np.copy()

            # 检查图像是否有严重的不均匀照明问题
            # 计算图像不同区域的亮度差异
            h, w, _ = img_np.shape
            top_brightness = np.mean(img_np[:h//3, :, :])
            bottom_brightness = np.mean(img_np[2*h//3:, :, :])
            left_brightness = np.mean(img_np[:, :w//3, :])
            right_brightness = np.mean(img_np[:, 2*w//3:, :])

            max_brightness_diff = max(
                abs(top_brightness - bottom_brightness),
                abs(left_brightness - right_brightness)
            )

            # 如果亮度差异大，说明有严重的不均匀照明问题
            has_severe_lighting_issue = max_brightness_diff > 50

            if has_severe_lighting_issue:
                logging.warning(f"页面 {page_num+1} 检测到严重的不均匀照明，亮度差异: {max_brightness_diff:.1f}")

                # 对于严重的不均匀照明问题，使用更强的预处理
                # 1. 应用高斯模糊获取背景
                blur_size = max(h, w) // 10
                blur_size = blur_size if blur_size % 2 == 1 else blur_size + 1  # 确保是奇数
                background = cv2.GaussianBlur(img_np, (blur_size, blur_size), 0)

                # 2. 使用背景减法去除不均匀照明
                # 将图像转换为浮点型
                img_float = img_np.astype(np.float32)
                background_float = background.astype(np.float32)

                # 计算差异并归一化
                diff = img_float - background_float + 128
                normalized = np.clip(diff, 0, 255).astype(np.uint8)

                # 使用处理后的图像
                denoised = normalized

                # 应用CLAHE进一步增强对比度
                gray = cv2.cvtColor(denoised, cv2.COLOR_RGB2GRAY)
                clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
                gray_enhanced = clahe.apply(gray)

                # 转换回RGB
                enhanced = cv2.cvtColor(gray_enhanced, cv2.COLOR_GRAY2RGB)
            else:
                # 对于正常照明的图像，使用标准预处理
                # 应用非局部均值去噪，比双边滤波更好地保留细节
                if "噪点多" in problems:
                    # 如果噪点多，使用更强的去噪
                    denoised = cv2.fastNlMeansDenoisingColored(img_np, None, 10, 10, 7, 21)
                else:
                    # 轻微去噪，保留文字细节
                    denoised = cv2.fastNlMeansDenoisingColored(img_np, None, 5, 5, 7, 21)

                # 应用锐化滤波器，增强文字边缘，但使用更保守的参数
                kernel = np.array([[-0.5,-0.5,-0.5], [-0.5,5,-0.5], [-0.5,-0.5,-0.5]])
                sharpened = cv2.filter2D(denoised, -1, kernel)

                # 应用CLAHE（对比度受限的自适应直方图均衡化）增强对比度
                # 但使用更保守的参数
                lab = cv2.cvtColor(sharpened, cv2.COLOR_RGB2LAB)
                l, a, b = cv2.split(lab)

                # 根据图像问题自适应调整CLAHE参数
                if "对比度低" in problems:
                    # 如果对比度低，使用适中的CLAHE
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                elif "过曝" in problems:
                    # 如果过曝，使用较弱的CLAHE
                    clahe = cv2.createCLAHE(clipLimit=1.0, tileGridSize=(8, 8))
                else:
                    # 默认参数 - 更保守
                    clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8, 8))

                cl = clahe.apply(l)
                enhanced_lab = cv2.merge((cl, a, b))
                enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2RGB)

                # 如果亮度低，增加亮度，但使用更保守的参数
                if "亮度低" in problems:
                    # 转换为HSV空间调整亮度
                    hsv = cv2.cvtColor(enhanced, cv2.COLOR_RGB2HSV)
                    h, s, v = cv2.split(hsv)
                    # 增加亮度，但不超过255，使用更保守的系数
                    v = np.clip(v * 1.2, 0, 255).astype(np.uint8)
                    enhanced_hsv = cv2.merge([h, s, v])
                    enhanced = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2RGB)

            # 转换为灰度图
            gray = cv2.cvtColor(enhanced, cv2.COLOR_RGB2GRAY)

            # 2. 使用更保守的二值化方法，确保保留文字

            # 首先，应用CLAHE增强对比度，但使用更保守的参数
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            gray_equalized = clahe.apply(gray)

            # 使用多种二值化方法结合，更好地处理文字和背景
            # 1. 自适应阈值 - 处理局部对比度变化
            binary_adaptive = cv2.adaptiveThreshold(
                gray_equalized,
                255,
                cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY,
                25,  # 块大小
                10   # 常数，较小的值可以保留更多细节
            )

            # 2. Otsu全局阈值 - 更好地分离前景和背景
            _, binary_otsu = cv2.threshold(
                gray_equalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
            )

            # 3. 结合两种二值化结果，取交集，保留更可靠的文本区域
            binary = cv2.bitwise_and(binary_adaptive, binary_otsu)

            # 检查黑色区域比例
            black_ratio = 1.0 - (np.sum(binary) / (255 * binary.size))
            logging.info(f"页面 {page_num+1} 黑色区域比例: {black_ratio:.1%}")

            # 如果黑色区域过多（超过40%），可能是阈值设置不当，使用更保守的方法
            if black_ratio > 0.4:
                logging.warning(f"页面 {page_num+1} 黑色区域过多，使用更保守的阈值")

                # 使用更保守的自适应阈值，增大块大小，减小常数
                binary = cv2.adaptiveThreshold(
                    gray_equalized,
                    255,
                    cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY,
                    35,  # 更大的块大小
                    5    # 更小的常数，保留更多文字
                )

                # 再次检查黑色区域比例
                black_ratio = 1.0 - (np.sum(binary) / (255 * binary.size))
                logging.info(f"页面 {page_num+1} 调整后黑色区域比例: {black_ratio:.1%}")

            # 确保是白底黑字
            if np.mean(binary) < 127:
                binary = cv2.bitwise_not(binary)
                logging.info(f"页面 {page_num+1} 反转图像，确保白底黑字")

            # 3. 降噪处理 - 非常轻微，确保不会丢失文字
            # 直接使用二值化结果，不进行额外的滤波
            denoised = binary

            # 4. 跳过曲面矫正，直接使用二值化结果
            # 曲面矫正可能会导致文字变形或丢失，为了确保文字完整性，我们跳过这一步
            processed = denoised

            # 记录跳过曲面矫正的原因
            logging.info(f"页面 {page_num+1} 跳过曲面矫正，确保文字不会丢失")

            # 5. 专门处理凌乱线条 - 使用专用函数

            # 保存原始处理结果，用于后续比较
            original_processed = processed.copy()

            # 使用专门的函数去除凌乱线条
            processed = remove_irregular_lines(processed)

            # 再次应用一次，处理可能遗漏的线条
            processed = remove_irregular_lines(processed)

            logging.info(f"页面 {page_num+1} 完成凌乱线条清理")

            # 6. 创建白底黑字的图像
            # 确保背景是白色(255)，文字是黑色(0)
            # 检查图像的主要颜色（背景色）
            # 计算图像的平均值，判断背景是黑色还是白色
            mean_value = np.mean(processed)
            if mean_value < 127:
                # 如果背景主要是黑色，则反转图像
                processed = cv2.bitwise_not(processed)
                logging.info(f"页面 {page_num+1} 反转图像，确保白底黑字")

            # 7. 评估处理效果并检测修复黑色区域
            # 计算处理前后的文本可读性差异
            # 使用OCR评估或简单的图像统计

            # 计算处理后图像的标准差，作为文本清晰度的指标
            processed_std = np.std(processed)
            original_std = np.std(cv2.cvtColor(original_img, cv2.COLOR_RGB2GRAY))

            # 检测是否有大面积黑色区域
            # 使用形态学操作找到大的黑色区域
            black_mask = cv2.bitwise_not(processed)  # 反转，使黑色区域变为白色
            kernel = np.ones((20, 20), np.uint8)  # 使用较大的核
            black_regions = cv2.morphologyEx(black_mask, cv2.MORPH_CLOSE, kernel)
            black_regions = cv2.morphologyEx(black_regions, cv2.MORPH_OPEN, kernel)

            # 查找大的黑色区域
            contours, _ = cv2.findContours(black_regions, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            large_black_regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                # 只考虑面积大于图像面积1%的区域
                if area > 0.01 * processed.shape[0] * processed.shape[1]:
                    large_black_regions.append(contour)

            # 如果存在大面积黑色区域，进行修复
            if large_black_regions:
                logging.warning(f"页面 {page_num+1} 检测到 {len(large_black_regions)} 个大面积黑色区域，进行修复")

                # 创建修复掩码
                repair_mask = np.zeros_like(processed)
                cv2.drawContours(repair_mask, large_black_regions, -1, 255, -1)

                # 使用原始图像的灰度信息修复这些区域
                gray_original = cv2.cvtColor(original_img, cv2.COLOR_RGB2GRAY)

                # 对原始图像进行自适应阈值处理，参数更保守
                repair_binary = cv2.adaptiveThreshold(
                    gray_original, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                    cv2.THRESH_BINARY, 35, 25  # 更大的块大小，更大的常数
                )

                # 确保是白底黑字
                if np.mean(repair_binary) < 127:
                    repair_binary = cv2.bitwise_not(repair_binary)

                # 只修复黑色区域
                processed_repaired = processed.copy()
                processed_repaired[repair_mask > 0] = repair_binary[repair_mask > 0]

                # 使用修复后的图像
                processed = processed_repaired
                logging.info(f"页面 {page_num+1} 已修复大面积黑色区域")

            # 检测和修复页面边缘的黑色线条
            # 使用Canny边缘检测找到页面边缘
            edges = cv2.Canny(processed, 50, 150)

            # 使用霍夫线变换检测直线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)

            # 如果检测到线条，检查是否有边缘线条
            if lines is not None:
                h, w = processed.shape
                edge_lines = []

                # 定义边缘区域
                edge_margin = int(min(h, w) * 0.05)  # 边缘区域为图像尺寸的5%

                for line in lines:
                    x1, y1, x2, y2 = line[0]

                    # 检查线条是否在边缘区域
                    if (x1 < edge_margin or x1 > w - edge_margin or
                        x2 < edge_margin or x2 > w - edge_margin or
                        y1 < edge_margin or y1 > h - edge_margin or
                        y2 < edge_margin or y2 > h - edge_margin):

                        edge_lines.append(line)

                # 如果检测到边缘线条，进行修复
                if edge_lines:
                    logging.info(f"页面 {page_num+1} 检测到 {len(edge_lines)} 条边缘线条，进行修复")

                    # 创建一个掩码，用于绘制线条
                    line_mask = np.zeros_like(processed)

                    # 在掩码上绘制线条，使用较粗的线条确保完全覆盖
                    for line in edge_lines:
                        x1, y1, x2, y2 = line[0]
                        cv2.line(line_mask, (x1, y1), (x2, y2), 255, 5)

                    # 将线条区域设为白色
                    processed[line_mask > 0] = 255

            # 如果处理后的标准差明显低于原始图像，可能表示文字细节丢失
            if processed_std < original_std * 0.7:
                logging.warning(f"页面 {page_num+1} 处理后文字清晰度可能降低，尝试备选方案")

                # 备选方案：使用更简单的处理方法
                # 只进行基本的二值化和对比度增强
                gray_original = cv2.cvtColor(original_img, cv2.COLOR_RGB2GRAY)

                # 使用自适应阈值，但参数更保守
                alt_binary = cv2.adaptiveThreshold(
                    gray_original, 255, cv2.ADAPTIVE_THRESH_MEAN_C,
                    cv2.THRESH_BINARY, 35, 25  # 更大的块大小，更大的常数
                )

                # 确保是白底黑字
                if np.mean(alt_binary) < 127:
                    alt_binary = cv2.bitwise_not(alt_binary)

                # 使用备选处理结果
                processed = alt_binary
                logging.info(f"页面 {page_num+1} 使用备选处理方案")

            # 8. 只清理页面边缘，保留文字
            h, w = processed.shape

            # 8.1 只清理页面边缘
            border_width = int(min(h, w) * 0.02)  # 边缘宽度为图像尺寸的2%

            # 创建一个全黑的掩码
            edge_mask = np.zeros_like(processed)

            # 在掩码上创建边缘区域（上、下、左、右四个边缘矩形）
            # 上边缘
            cv2.rectangle(edge_mask, (0, 0), (w, border_width), 255, -1)
            # 下边缘
            cv2.rectangle(edge_mask, (0, h - border_width), (w, h), 255, -1)
            # 左边缘
            cv2.rectangle(edge_mask, (0, 0), (border_width, h), 255, -1)
            # 右边缘
            cv2.rectangle(edge_mask, (w - border_width, 0), (w, h), 255, -1)

            # 检测边缘区域中的黑色像素
            edge_black_pixels = np.logical_and(processed == 0, edge_mask == 255)
            edge_black_count = np.sum(edge_black_pixels)

            # 如果边缘区域有黑色像素，进行清理
            if edge_black_count > 0:
                logging.info(f"页面 {page_num+1} 检测到边缘有 {edge_black_count} 个黑色像素，进行清理")

                # 将边缘区域的黑色像素设为白色
                processed[edge_black_pixels] = 255

            # 8.2 只检测和清理边缘线条，不处理页面内部
            # 创建一个更宽的边缘掩码用于线条检测
            edge_width_for_lines = int(min(h, w) * 0.05)  # 边缘宽度为图像尺寸的5%
            edge_mask_for_lines = np.zeros_like(processed)

            # 定义边缘区域
            cv2.rectangle(edge_mask_for_lines, (0, 0), (w, edge_width_for_lines), 255, -1)  # 上边缘
            cv2.rectangle(edge_mask_for_lines, (0, h-edge_width_for_lines), (w, h), 255, -1)  # 下边缘
            cv2.rectangle(edge_mask_for_lines, (0, 0), (edge_width_for_lines, h), 255, -1)  # 左边缘
            cv2.rectangle(edge_mask_for_lines, (w-edge_width_for_lines, 0), (w, h), 255, -1)  # 右边缘

            # 使用边缘检测
            edges = cv2.Canny(processed, 50, 150)

            # 使用霍夫线变换检测直线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=100, maxLineGap=10)

            # 如果检测到线条，只清理边缘线条
            if lines is not None:
                edge_lines = []

                for line in lines:
                    x1, y1, x2, y2 = line[0]

                    # 检查线条是否在边缘区域
                    if edge_mask_for_lines[y1, x1] == 255 or edge_mask_for_lines[y2, x2] == 255:
                        edge_lines.append(line)

                # 如果检测到边缘线条，进行修复
                if edge_lines:
                    logging.info(f"页面 {page_num+1} 检测到 {len(edge_lines)} 条边缘线条，进行修复")

                    # 创建一个掩码，用于绘制线条
                    line_mask = np.zeros_like(processed)

                    # 在掩码上绘制线条，使用较粗的线条确保完全覆盖
                    for line in edge_lines:
                        x1, y1, x2, y2 = line[0]
                        cv2.line(line_mask, (x1, y1), (x2, y2), 255, 5)

                    # 只将线条区域的黑色像素设为白色
                    line_black_pixels = np.logical_and(processed == 0, line_mask == 255)
                    processed[line_black_pixels] = 255

            # 8.3 只清理页面边缘的噪点，保留文字
            # 创建一个边缘掩码
            edge_width = int(min(h, w) * 0.03)  # 边缘宽度为图像尺寸的3%
            edge_mask = np.zeros_like(processed)

            # 定义边缘区域
            cv2.rectangle(edge_mask, (0, 0), (w, edge_width), 255, -1)  # 上边缘
            cv2.rectangle(edge_mask, (0, h-edge_width), (w, h), 255, -1)  # 下边缘
            cv2.rectangle(edge_mask, (0, 0), (edge_width, h), 255, -1)  # 左边缘
            cv2.rectangle(edge_mask, (w-edge_width, 0), (w, h), 255, -1)  # 右边缘

            # 只在边缘区域清理小噪点
            # 标记连通区域
            num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(
                cv2.bitwise_not(processed),
                connectivity=8
            )

            # 计算每个连通区域的面积
            cleaned_count = 0
            for i in range(1, num_labels):  # 跳过背景（标签0）
                area = stats[i, cv2.CC_STAT_AREA]

                # 获取连通区域的边界框
                x = stats[i, cv2.CC_STAT_LEFT]
                y = stats[i, cv2.CC_STAT_TOP]
                width = stats[i, cv2.CC_STAT_WIDTH]
                height = stats[i, cv2.CC_STAT_HEIGHT]

                # 检查连通区域是否在边缘区域
                is_in_edge = (
                    (y < edge_width) or  # 上边缘
                    (y + height > h - edge_width) or  # 下边缘
                    (x < edge_width) or  # 左边缘
                    (x + width > w - edge_width)  # 右边缘
                )

                # 如果连通区域在边缘且面积小于阈值，认为是噪点，进行清理
                # 使用较小的阈值，避免清理掉文字
                area_threshold = 0.0003 * processed.size  # 0.03%的图像面积
                if is_in_edge and area < area_threshold:
                    # 将这个小连通区域设为白色
                    processed[labels == i] = 255
                    cleaned_count += 1

            if cleaned_count > 0:
                logging.info(f"页面 {page_num+1} 清理了边缘区域的 {cleaned_count} 个小噪点")

            # 9. 只检测和清理边缘的大面积黑色区域，保留文字
            # 创建一个更宽的边缘掩码用于大面积黑色区域检测
            edge_width_for_regions = int(min(h, w) * 0.08)  # 边缘宽度为图像尺寸的8%
            edge_mask_for_regions = np.zeros_like(processed)

            # 定义边缘区域
            cv2.rectangle(edge_mask_for_regions, (0, 0), (w, edge_width_for_regions), 255, -1)  # 上边缘
            cv2.rectangle(edge_mask_for_regions, (0, h-edge_width_for_regions), (w, h), 255, -1)  # 下边缘
            cv2.rectangle(edge_mask_for_regions, (0, 0), (edge_width_for_regions, h), 255, -1)  # 左边缘
            cv2.rectangle(edge_mask_for_regions, (w-edge_width_for_regions, 0), (w, h), 255, -1)  # 右边缘

            # 只在边缘区域查找黑色区域
            edge_black_mask = cv2.bitwise_and(cv2.bitwise_not(processed), edge_mask_for_regions)

            # 使用形态学操作找到大的黑色区域
            kernel_large = np.ones((5, 5), np.uint8)

            # 使用闭运算填充黑色区域内的小空洞
            closed = cv2.morphologyEx(edge_black_mask, cv2.MORPH_CLOSE, kernel_large)

            # 使用开运算去除细线，保留大块区域
            opened_large = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel_large)

            # 查找大的黑色区域
            contours, _ = cv2.findContours(opened_large, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            large_black_regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                # 只考虑面积大于图像面积0.1%的区域
                if area > 0.001 * processed.shape[0] * processed.shape[1]:
                    large_black_regions.append(contour)

            # 如果存在大面积黑色区域，进行清理
            if large_black_regions:
                logging.warning(f"页面 {page_num+1} 检测到边缘有 {len(large_black_regions)} 个大面积黑色区域，进行清理")

                # 创建修复掩码
                repair_mask = np.zeros_like(processed)
                cv2.drawContours(repair_mask, large_black_regions, -1, 255, -1)

                # 将这些大面积黑色区域设为白色
                processed[repair_mask > 0] = 255

            # 10. 最终质量检查 - 更保守的处理
            # 计算黑色像素比例
            black_ratio = 1.0 - (np.sum(processed) / (255 * processed.size))
            logging.info(f"页面 {page_num+1} 最终黑色区域比例: {black_ratio:.1%}")

            # 如果黑色区域过多，可能是处理不当，但我们不再使用替代方案
            # 而是保留当前处理结果，确保文字不会丢失
            if black_ratio > 0.4:  # 黑色区域超过40%
                logging.warning(f"页面 {page_num+1} 黑色区域比例较高 ({black_ratio:.1%})，但保留当前处理结果以确保文字不丢失")

                # 只清理边缘区域，不处理内部
                edge_mask = np.zeros_like(processed)
                border_width = int(min(h, w) * 0.03)

                # 上边缘
                cv2.rectangle(edge_mask, (0, 0), (w, border_width), 255, -1)
                # 下边缘
                cv2.rectangle(edge_mask, (0, h - border_width), (w, h), 255, -1)
                # 左边缘
                cv2.rectangle(edge_mask, (0, 0), (border_width, h), 255, -1)
                # 右边缘
                cv2.rectangle(edge_mask, (w - border_width, 0), (w, h), 255, -1)

                # 只清理边缘
                processed[edge_mask == 255] = 255

            # 11. 添加白色边框，确保页面边缘干净
            # 创建一个较小的白色边框，避免覆盖文字
            border_width_final = int(min(h, w) * 0.01)  # 最终边框宽度为图像尺寸的1%

            # 创建一个带白色边框的新图像
            bordered = cv2.copyMakeBorder(
                processed,
                border_width_final, border_width_final, border_width_final, border_width_final,
                cv2.BORDER_CONSTANT,
                value=255
            )

            # 使用带边框的图像
            processed = bordered

            # 12. 转换回RGB格式
            processed_rgb = cv2.cvtColor(processed, cv2.COLOR_GRAY2RGB)

            # 13. 转换为PIL图像
            processed_img = Image.fromarray(processed_rgb)

            # 14. 将处理后的图像添加到新的PDF文档
            img_bytes = io.BytesIO()
            # 使用高质量的PNG格式，确保文字清晰
            processed_img.save(img_bytes, format="PNG", compress_level=1)  # 低压缩级别，保持高质量
            img_bytes.seek(0)

            # 创建新页面
            new_page = output_doc.new_page(width=page.rect.width, height=page.rect.height)

            # 插入处理后的图像，确保居中显示
            new_page.insert_image(new_page.rect, stream=img_bytes.getvalue(), keep_proportion=True)

            logging.info(f"已处理页面 {page_num+1}/{len(doc)}: 去除背景、增强文字、保留文字清晰度、优化文件大小")

        # 保存处理后的PDF - 优化参数提高速度
        output_doc.save(
            output_path,
            garbage=3,         # 减少垃圾收集级别以提高速度
            deflate=True,      # 使用deflate压缩
            clean=True,        # 清理冗余内容
            linear=False       # 关闭线性化以提高保存速度
        )
        output_doc.close()
        doc.close()

        logging.info(f"背景去除完成: {output_path}")
        return True

    except Exception as e:
        logging.error(f"去除背景时出错: {str(e)}")
        traceback.print_exc()

        # 如果处理失败，尝试复制原始文件
        try:
            import shutil
            shutil.copy(input_path, output_path)
            logging.warning(f"处理失败，已复制原始文件: {output_path}")
            return False
        except Exception as copy_error:
            logging.error(f"复制原始文件时出错: {str(copy_error)}")
            return False

def convert_to_word(input_path, output_path):
    """
    将PDF转换为Word文档，保持原文本型PDF文件的页面、段落、字体、颜色等要素不变
    使用更可靠的方法，避免乱码问题
    """
    try:
        logging.info(f"将PDF转换为Word: {input_path} -> {output_path}")

        # 检查是否为文本型PDF
        is_text_pdf = not is_scanned_pdf(input_path)

        if is_text_pdf:
            logging.info("检测到文本型PDF，使用优化的文本提取方法")

            # 使用pdf2docx进行转换，设置更高的DPI和更精确的布局保留
            from pdf2docx import Converter
            cv = Converter(input_path)

            # 设置转换参数，保留原始格式
            # 使用更高级的设置，确保文本正确提取和保持原始格式
            cv.convert(output_path, start=0, end=None, zoom=2.0,  # 更高的分辨率
                      line_overlap_threshold=0.2,  # 更精确的行识别
                      line_break_width_threshold=0.05,  # 更精确的断行识别
                      line_break_free_space_threshold=0.05,  # 更精确的空格识别
                      line_separate_threshold=0.05,  # 更精确的分隔识别
                      line_join_threshold=0.05,  # 更精确的连接识别
                      debug=False,  # 关闭调试输出
                      multi_processing=True,  # 启用多进程处理
                      kwargs={
                          'connected_border_tolerance': 0,  # 更精确的边界识别
                          'min_svg_gap_dx': 5.0,  # 更精确的SVG间隙识别
                          'min_svg_gap_dy': 5.0,  # 更精确的SVG间隙识别
                          'min_svg_w': 5.0,  # 更精确的SVG宽度识别
                          'min_svg_h': 5.0,  # 更精确的SVG高度识别
                          'preserve_font': True,  # 保留原始字体信息
                          'preserve_space': True,  # 保留原始空格
                          'preserve_vertical_text': True,  # 保留垂直文本
                      })
            cv.close()
            logging.info("使用优化参数的pdf2docx模块转换成功")

            # 转换完成后，进行字体后处理
            logging.info("开始进行字体后处理，确保保留原始字体...")

            # 提取PDF中的字体信息
            pdf_fonts = extract_pdf_fonts(input_path)
            logging.info(f"从PDF中提取到 {len(pdf_fonts)} 种字体")

            # 提取文本样式信息
            text_styles = extract_text_styles(input_path)
            logging.info(f"从PDF中提取到 {len(text_styles)} 种文本样式")

            # 打开Word文档进行字体修复
            import docx
            from docx.shared import Pt
            doc = docx.Document(output_path)

            # 默认字体映射
            default_font_map = {
                # 中文字体映射
                'SimSun': '宋体',
                'SimHei': '黑体',
                'KaiTi': '楷体',
                'FangSong': '仿宋',
                'Microsoft YaHei': '微软雅黑',
                'STSong': '宋体',
                'STHeiti': '黑体',
                'STKaiti': '楷体',
                'STFangsong': '仿宋',
                'STXihei': '华文细黑',
                'STZhongsong': '华文中宋',
                'STLiti': '隶书',
                'STXingkai': '行楷',
                'STHupo': '琥珀',
                'STCaiyun': '彩云',
                'STXinwei': '新魏',
                'STYuanti': '圆体',
                'STShuTi': '舒体',
                'STBaosong': '宝宋',
                'STYingkai': '楷体',
                'STXinkai': '新楷体',
                'STZhongkai': '中楷',
                'STFangsong': '仿宋',
                'STSong': '宋体',
                'STXinwei': '新魏',
                'STXihei': '细黑',
                'STKaiti': '楷体',
                'STLiti': '隶书',
                'STYuanti': '圆体',
                'STShuTi': '舒体',
                'STBaosong': '宝宋',
                'STYingkai': '楷体',
                'STXinkai': '新楷体',
                'STZhongkai': '中楷',
                'STFangsong': '仿宋',
                'STCaiyun': '彩云',
                'STHupo': '琥珀',
                'STLiti': '隶书',
                'STXingkai': '行楷',
                'STXinwei': '新魏',
                'STZhongsong': '中宋',
                'STXihei': '细黑',
                'STKaiti': '楷体',
                'STSong': '宋体',
                'STFangsong': '仿宋',
                'STXinwei': '新魏',
                'STXihei': '细黑',
                'STKaiti': '楷体',
                'STLiti': '隶书',
                'STYuanti': '圆体',
                'STShuTi': '舒体',
                'STBaosong': '宝宋',
                'STYingkai': '楷体',
                'STXinkai': '新楷体',
                'STZhongkai': '中楷',
                'STFangsong': '仿宋',
                'STCaiyun': '彩云',
                'STHupo': '琥珀',
                'STLiti': '隶书',
                'STXingkai': '行楷',
                'STXinwei': '新魏',
                'STZhongsong': '中宋',
                'STXihei': '细黑',
                'STKaiti': '楷体',
                'STSong': '宋体',
                'STFangsong': '仿宋',

                # 英文字体映射
                'Arial': 'Arial',
                'Times New Roman': 'Times New Roman',
                'Courier New': 'Courier New',
                'Calibri': 'Calibri',
                'Verdana': 'Verdana',
                'Tahoma': 'Tahoma',
                'Georgia': 'Georgia',
                'Garamond': 'Garamond',
                'Bookman': 'Bookman',
                'Palatino': 'Palatino',
                'Century': 'Century',
                'Century Gothic': 'Century Gothic',
                'Bookman Old Style': 'Bookman Old Style',
                'Book Antiqua': 'Book Antiqua',
                'Arial Narrow': 'Arial Narrow',
                'Arial Black': 'Arial Black',
                'Impact': 'Impact',
                'Lucida Sans': 'Lucida Sans',
                'Lucida Console': 'Lucida Console',
                'Comic Sans MS': 'Comic Sans MS',
                'Trebuchet MS': 'Trebuchet MS',
                'Symbol': 'Symbol',
                'Wingdings': 'Wingdings',
                'Webdings': 'Webdings',

                # 通用映射
                'Serif': 'Times New Roman',
                'Sans-Serif': 'Arial',
                'Monospace': 'Courier New',
                'Cursive': 'Comic Sans MS',
                'Fantasy': 'Impact',

                # 默认字体
                'default': '宋体',
            }

            # 应用字体修复
            font_applied_count = 0

            # 获取主要字体和大小
            main_font = '宋体'
            main_size = 12

            # 如果有文本样式信息，使用出现频率最高的字体和大小
            if text_styles and len(text_styles) > 0:
                # 统计字体出现频率
                font_counter = {}
                size_counter = {}

                for style in text_styles:
                    font = style['font']
                    size = style['size']

                    if font in font_counter:
                        font_counter[font] += 1
                    else:
                        font_counter[font] = 1

                    if size in size_counter:
                        size_counter[size] += 1
                    else:
                        size_counter[size] = 1

                # 获取出现频率最高的字体和大小
                if font_counter:
                    main_font = max(font_counter.items(), key=lambda x: x[1])[0]
                if size_counter:
                    main_size = max(size_counter.items(), key=lambda x: x[1])[0]

                logging.info(f"主要字体: {main_font}, 主要字体大小: {main_size}")

            # 如果字体名称在映射表中，使用映射后的名称
            if main_font in default_font_map:
                main_font = default_font_map[main_font]

            # 应用字体到段落
            for paragraph in doc.paragraphs:
                for run in paragraph.runs:
                    # 应用主要字体
                    run.font.name = main_font

                    # 应用字体大小
                    if main_size > 0:
                        run.font.size = Pt(main_size)

                    font_applied_count += 1

            # 应用字体到表格
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                # 应用主要字体
                                run.font.name = main_font

                                # 应用字体大小
                                if main_size > 0:
                                    run.font.size = Pt(main_size)

                                font_applied_count += 1

            # 保存文档
            doc.save(output_path)
            logging.info(f"字体后处理完成，应用了 {font_applied_count} 处字体修复")
            logging.info("已完成转换，保留原始布局和字体")

            return True
        else:
            logging.info("检测到扫描型PDF，使用图像转换方法")

            # 对于扫描型PDF，使用不同的方法
            # 先将PDF转换为图像，然后将图像插入到Word文档中
            import docx
            from docx.shared import Inches
            from pdf2image import convert_from_path

            # 将PDF转换为图像
            images = convert_from_path(input_path, dpi=300)

            # 创建一个新的Word文档
            doc = docx.Document()

            # 设置页面边距为0
            for section in doc.sections:
                section.top_margin = Inches(0.5)
                section.bottom_margin = Inches(0.5)
                section.left_margin = Inches(0.5)
                section.right_margin = Inches(0.5)

            # 将每个图像添加到文档中
            for i, image in enumerate(images):
                # 保存图像为临时文件
                temp_img_path = f"temp_img_{i}.png"
                image.save(temp_img_path, "PNG")

                # 添加图像到文档
                doc.add_picture(temp_img_path, width=Inches(6.5))

                # 如果不是最后一页，添加分页符
                if i < len(images) - 1:
                    doc.add_page_break()

                # 删除临时文件
                os.remove(temp_img_path)

            # 保存文档
            doc.save(output_path)
            logging.info("已将扫描型PDF转换为包含图像的Word文档")

            return True

    except Exception as e:
        logging.error(f"转换PDF为Word时出错: {str(e)}")

        # 创建一个简单的错误提示文档
        import docx
        doc = docx.Document()
        doc.add_paragraph(f"转换PDF时出错: {str(e)}").bold = True
        doc.save(output_path)

        return False
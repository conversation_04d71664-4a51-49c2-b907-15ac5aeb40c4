@echo off
chcp 65001 > nul
echo 正在启动PDF编辑工具...

REM 检查虚拟环境是否存在
if not exist venv (
    echo 虚拟环境不存在！请先运行 setup_venv.bat 设置环境。
    pause
    exit /b 1
)

REM 检查Poppler是否存在
if not exist poppler\Library\bin (
    echo Poppler库不存在或不完整！请重新运行 setup_venv.bat 设置环境。
    pause
    exit /b 1
)

REM 设置Poppler环境变量
set PATH=%PATH%;%CD%\poppler\Library\bin

REM 激活虚拟环境并运行应用
echo 正在启动应用程序...
call venv\Scripts\activate.bat
python app.py

REM 如果应用程序异常退出，保持窗口打开
if %errorlevel% neq 0 (
    echo.
    echo 应用程序异常退出，错误代码: %errorlevel%
    pause
)

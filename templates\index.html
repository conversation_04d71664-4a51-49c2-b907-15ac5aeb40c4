<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF编辑工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>PDF编辑工具</h1>
            <p>上传PDF文件进行处理</p>
        </header>

        <div class="upload-container">
            <div class="upload-area" id="upload-area">
                <i class="fas fa-file-pdf"></i>
                <p>拖放PDF文件到这里或点击上传</p>
                <input type="file" id="file-input" multiple accept=".pdf" style="display: none;">
                <button id="upload-btn" class="btn">选择文件</button>
            </div>
        </div>

        <div class="file-list-container" id="file-list-container" style="display: none;">
            <h2>已上传文件</h2>
            <ul id="file-list"></ul>
        </div>

        <div class="processing-options" id="processing-options">
            <h2>处理选项</h2>
            <div class="buttons">
                <button id="preview-btn" class="btn action-btn">
                    <i class="fas fa-eye"></i> 预览
                    <span class="tooltip">预览检测到的红头和公章</span>
                </button>
                <button id="remove-header-btn" class="btn action-btn">
                    <i class="fas fa-cut"></i> 去红头
                    <span class="tooltip">删除PDF文件的红头和公章</span>
                </button>
                <button id="force-process-btn" class="btn action-btn">
                    <i class="fas fa-hammer"></i> 强制去红头
                    <span class="tooltip">强制转为图片型PDF后处理</span>
                </button>
                <button id="remove-background-btn" class="btn action-btn">
                    <i class="fas fa-eraser"></i> 去背景
                    <span class="tooltip">去除背景杂色，增强文字清晰度</span>
                </button>
                <button id="convert-to-word-btn" class="btn action-btn">
                    <i class="fas fa-file-word"></i> 转Word
                    <span class="tooltip">将PDF转换为Word文档</span>
                </button>
            </div>
        </div>

        <div class="preview-container" id="preview-container" style="display: none;">
            <h2>预览</h2>
            <div class="preview-info">
                <p id="preview-file-type"></p>
                <p id="preview-detection-info"></p>
            </div>
            <div class="preview-pages" id="preview-pages"></div>
            <div class="preview-buttons">
                <button id="preview-process-btn" class="btn">去红头</button>
                <button id="preview-cancel-btn" class="btn">取消</button>
            </div>
        </div>

        <div class="progress-container" id="progress-container" style="display: none;">
            <h2>处理中...</h2>
            <div class="progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            <p id="progress-text">正在处理文件...</p>
        </div>

        <div class="result-container" id="result-container" style="display: none;">
            <h2>处理完成</h2>
            <p id="result-message"></p>
        </div>
    </div>

    <footer>
        <p>PDF编辑工具 &copy; 2025</p>
        <div class="network-info">
            <p>局域网访问地址: <span id="network-address">加载中...</span></p>
            <button id="cleanup-btn" class="btn btn-sm" title="清理临时文件">清理临时文件</button>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>

@echo off
echo PDF Editor - One-Click Installation
echo This script will install everything needed to run the PDF Editor

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed! Please install Python 3.8 or higher.
    echo You can download Python from https://www.python.org/downloads/
    echo IMPORTANT: Check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Create virtual environment directly with venv module
echo Creating virtual environment...
if not exist venv (
    python -m venv venv
) else (
    echo Virtual environment already exists, skipping creation.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Upgrade pip to latest version
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install wheel package first
echo Installing wheel package...
python -m pip install wheel

REM Install all required packages individually
echo Installing dependencies (this may take a few minutes)...
python -m pip install Flask==2.0.1
python -m pip install flask-cors==3.0.10
python -m pip install Werkzeug==2.0.1
python -m pip install Jinja2==3.0.1
python -m pip install itsdangerous==2.0.1
python -m pip install click==8.0.1
python -m pip install Six==1.16.0
python -m pip install numpy==1.19.5
python -m pip install Pillow==8.3.2
python -m pip install PyMuPDF==1.18.17
python -m pip install opencv-python-headless==********
python -m pip install pdf2image==1.16.0
python -m pip install python-docx==0.8.11
python -m pip install pdf2docx==0.5.6

REM Create necessary directories
echo Creating necessary directories...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

REM Download Poppler automatically
echo Downloading Poppler...
if not exist poppler (
    mkdir poppler
    
    REM Try to download using PowerShell
    powershell -Command "& {try { $ProgressPreference = 'SilentlyContinue'; Invoke-WebRequest -Uri 'https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip' -OutFile 'poppler.zip'; exit 0 } catch { exit 1 }}"
    
    if %errorlevel% neq 0 (
        echo PowerShell download failed. Trying with bitsadmin...
        bitsadmin /transfer PopplerDownload /download /priority high https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip %CD%\poppler.zip
    )
    
    if not exist poppler.zip (
        echo Automatic download failed.
        echo.
        echo Please download Poppler manually:
        echo 1. Download from: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
        echo 2. Save the file as "poppler.zip" in the current directory
        echo.
        set /p answer=Press Enter when you have downloaded the file, or type 'skip' to skip: 
        if /i "%answer%"=="skip" (
            echo Skipping Poppler installation.
            goto :skip_poppler
        )
    )
    
    echo Extracting Poppler...
    powershell -Command "& {try { Expand-Archive -Path 'poppler.zip' -DestinationPath 'poppler' -Force; exit 0 } catch { exit 1 }}"
    
    if %errorlevel% neq 0 (
        echo PowerShell extraction failed.
        echo Please extract poppler.zip manually and rename the folder to "poppler"
        pause
    ) else (
        echo Poppler extracted successfully.
        if exist poppler.zip del poppler.zip
    )
)

:skip_poppler

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo WARNING: Poppler installation not detected correctly.
    echo The application may not work properly without Poppler.
    echo.
    echo Please ensure:
    echo 1. Download from: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
    echo 2. Extract the ZIP file
    echo 3. Rename the folder to "poppler"
    echo 4. Place it in the current directory
    echo.
    pause
)

echo.
echo Installation complete!
echo Use simple_run.bat to start the application.
echo.
pause

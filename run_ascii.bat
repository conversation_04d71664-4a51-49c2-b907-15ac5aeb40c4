@echo off
echo Starting PDF Editor...

REM Check if virtual environment exists
if not exist venv (
    echo Virtual environment does not exist! Please run setup_ascii.bat first.
    pause
    exit /b 1
)

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo Poppler library does not exist or is incomplete! Please run setup_ascii.bat again.
    pause
    exit /b 1
)

REM Set Poppler environment variable
set PATH=%PATH%;%CD%\poppler\Library\bin

REM Activate virtual environment and run application
echo Starting application...
call venv\Scripts\activate.bat
python app.py

REM If application exits abnormally, keep window open
if %errorlevel% neq 0 (
    echo.
    echo Application exited abnormally with error code: %errorlevel%
    pause
)

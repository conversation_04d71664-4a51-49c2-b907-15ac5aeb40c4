#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试统一处理算法效果
"""

import os
import cv2
import numpy as np
from pdf2image import convert_from_path

def count_red_pixels(image):
    """统计红色像素数量"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = mask1 + mask2
    return cv2.countNonZero(red_mask)

def test_unified_algorithm():
    """测试统一处理算法"""
    print("="*60)
    print("测试统一处理算法效果")
    print("="*60)
    
    if not os.path.exists("test_unified_4.pdf"):
        print("处理后文件不存在")
        return
    
    # 比较处理前后
    try:
        orig_images = convert_from_path('sl/4.pdf', dpi=150)
        proc_images = convert_from_path('test_unified_4.pdf', dpi=150)
        
        print(f"原始文件页数: {len(orig_images)}")
        print(f"处理后文件页数: {len(proc_images)}")
        
        for i, (orig, proc) in enumerate(zip(orig_images[:3], proc_images[:3])):
            print(f"\n页面{i+1}:")
            
            orig_cv = cv2.cvtColor(np.array(orig), cv2.COLOR_RGB2BGR)
            proc_cv = cv2.cvtColor(np.array(proc), cv2.COLOR_RGB2BGR)
            
            orig_red = count_red_pixels(orig_cv)
            proc_red = count_red_pixels(proc_cv)
            
            total = orig_cv.shape[0] * orig_cv.shape[1]
            orig_ratio = orig_red / total * 100
            proc_ratio = proc_red / total * 100
            reduction = (orig_red - proc_red) / orig_red * 100 if orig_red > 0 else 0
            
            print(f"  原始红色像素: {orig_red} ({orig_ratio:.2f}%)")
            print(f"  处理后红色像素: {proc_red} ({proc_ratio:.2f}%)")
            print(f"  红色像素减少: {reduction:.1f}%")
            
            if reduction > 95:
                print(f"  ✓ 处理效果优秀")
            elif reduction > 85:
                print(f"  ⚠ 处理效果良好")
            else:
                print(f"  ✗ 处理效果不佳")
                
    except Exception as e:
        print(f"测试时出错: {str(e)}")

if __name__ == "__main__":
    test_unified_algorithm()

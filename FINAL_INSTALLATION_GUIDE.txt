PDF EDITOR - <PERSON><PERSON><PERSON> INSTALLATION GUIDE
===================================

This is the simplest installation method that should work on all Windows systems.

PREREQUISITES
------------
- Windows 7/8/10/11
- Python 3.8 or higher (https://www.python.org/downloads/)
- Internet connection (for downloading dependencies)

INSTALLATION STEPS
----------------
1. Install Python (if not already installed)
   - Download from https://www.python.org/downloads/
   - IMPORTANT: Check "Add Python to PATH" during installation
   - IMPORTANT: Check "Install pip" during installation

2. Right-click on install_wheels.bat and select "Run as administrator"
   - This script will install all dependencies without compilation
   - It will install each package individually to avoid dependency conflicts

3. When prompted, download and install Poppler:
   - Download from: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
   - Extract the ZIP file
   - Rename the extracted folder to "poppler" (all lowercase)
   - Move the "poppler" folder to the same directory as the application files

4. After installation is complete, use simple_run.bat to start the application

RUNNING THE APPLICATION
---------------------
1. Double-click simple_run.bat
   - This will start the application on port 5001

2. Open a web browser and navigate to:
   - http://127.0.0.1:5001 (local access)
   - http://[your-ip-address]:5001 (network access)

TROUBLESHOOTING
--------------

1. "Python is not recognized as an internal or external command"
   - Make sure Python is installed and added to PATH
   - Restart your computer after installing Python

2. "No module named 'venv'"
   - Install Python with the "pip" option checked
   - Or run: python -m pip install virtualenv

3. "poppler\Library\bin" not found
   - Make sure you've downloaded the correct Poppler version
   - Extract the ZIP file completely
   - Rename the folder to "poppler" (all lowercase)
   - Place it in the same directory as the application files

4. If all else fails, try the manual installation:
   - Open Command Prompt as administrator
   - Navigate to the application directory
   - Run: python -m venv venv
   - Run: venv\Scripts\activate.bat
   - Run: pip install --upgrade pip
   - Run: pip install wheel
   - Run: pip install Flask==2.0.1 flask-cors==3.0.10 Werkzeug==2.0.1
   - Run: pip install PyMuPDF==1.18.17 opencv-python-headless==********
   - Run: pip install numpy==1.19.5 Pillow==8.3.2
   - Run: pip install pdf2image==1.16.0 python-docx==0.8.11 pdf2docx==0.5.6
   - Download and install Poppler as described above
   - Run: python app.py

TECHNICAL SUPPORT
---------------
If you encounter any issues not covered in this guide, please contact technical support.

:root {
    --primary-color: #e53935;
    --primary-light: #ff6f60;
    --primary-dark: #ab000d;
    --secondary-color: #f5f5f5;
    --text-color: #333;
    --text-light: #fff;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

header {
    text-align: center;
    margin-bottom: 2rem;
}

header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.upload-container {
    margin-bottom: 2rem;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    border: 2px dashed var(--primary-color);
    border-radius: var(--border-radius);
    background-color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
}

.upload-area:hover {
    background-color: #eee;
}

.upload-area i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    margin-top: 1rem;
}

.btn:hover {
    background-color: var(--primary-dark);
}

.file-list-container {
    margin-bottom: 2rem;
}

.file-list-container h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

#file-list {
    list-style: none;
}

#file-list li {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    box-shadow: var(--box-shadow);
}

#file-list li i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 1rem;
}

#file-list li .file-name {
    flex-grow: 1;
}

#file-list li .remove-file {
    color: var(--primary-color);
    cursor: pointer;
    font-size: 1.2rem;
}

.processing-options {
    margin-bottom: 2rem;
}

.processing-options h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.action-btn {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    position: relative;
}

.action-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* 禁用状态的按钮样式 */
.btn.disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn.disabled:hover {
    background-color: #ccc;
}

.action-btn .tooltip {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.action-btn:hover .tooltip {
    opacity: 1;
    visibility: visible;
}

.progress-container {
    margin-bottom: 2rem;
    text-align: center;
}

.progress-container h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.progress {
    height: 20px;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    width: 0;
    transition: width 0.3s ease;
}

.result-container {
    text-align: center;
    margin-bottom: 2rem;
}

.result-container h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.result-container p {
    margin-bottom: 1rem;
}

.preview-container {
    margin-bottom: 2rem;
}

.preview-container h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.preview-info {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--secondary-color);
    border-radius: var(--border-radius);
}

.preview-pages {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.preview-page {
    position: relative;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    overflow: hidden;
    width: 200px;
}

.preview-page img {
    width: 100%;
    display: block;
}

.preview-page .page-number {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
}

.preview-page .detection {
    position: absolute;
    border: 2px solid red;
    background-color: rgba(255, 0, 0, 0.2);
    pointer-events: none;
}

.preview-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

footer {
    text-align: center;
    padding: 1rem;
    background-color: var(--primary-color);
    color: var(--text-light);
}

.network-info {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    display: inline-block;
}

.network-info p {
    font-size: 0.9rem;
    margin: 0;
}

#network-address {
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
}

#cleanup-btn {
    margin-top: 0.5rem;
    background-color: rgba(0, 0, 0, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.2rem 0.5rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

#cleanup-btn:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

#cleanup-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

@echo off
chcp 65001 > nul
echo 正在设置PDF编辑工具的虚拟环境...

REM 检查Python是否已安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装！请先安装Python 3.8或更高版本。
    echo 可以从 https://www.python.org/downloads/ 下载Python。
    pause
    exit /b 1
)

REM 检查pip是否已安装
python -m pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo pip未安装！请先安装pip。
    pause
    exit /b 1
)

REM 检查virtualenv是否已安装
python -m pip show virtualenv > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装virtualenv...
    python -m pip install virtualenv
)

REM 创建虚拟环境
if not exist venv (
    echo 正在创建虚拟环境...
    python -m virtualenv venv
) else (
    echo 虚拟环境已存在，跳过创建步骤。
)

REM 激活虚拟环境并安装依赖
echo 正在安装依赖项...
call venv\Scripts\activate.bat
python -m pip install -r requirements.txt

REM 安装poppler（pdf2image依赖）
echo 正在下载并安装Poppler...
if not exist poppler (
    mkdir poppler

    REM 使用更可靠的方式下载文件
    echo 正在下载Poppler...
    powershell -NoProfile -ExecutionPolicy Bypass -Command ^
        "$ProgressPreference = 'SilentlyContinue'; ^
        Invoke-WebRequest -Uri 'https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip' -OutFile 'poppler.zip'"

    if not exist poppler.zip (
        echo 下载失败，请检查网络连接或手动下载Poppler。
        echo 下载地址: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
        pause
        exit /b 1
    )

    echo 正在解压Poppler...
    powershell -NoProfile -ExecutionPolicy Bypass -Command ^
        "$ProgressPreference = 'SilentlyContinue'; ^
        Expand-Archive -Path 'poppler.zip' -DestinationPath 'poppler' -Force"

    if exist poppler.zip del poppler.zip
    echo Poppler安装完成。
)

REM 创建uploads和processed目录
echo 正在创建必要的目录...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

echo.
echo 设置完成！
echo 使用 run.bat 启动应用程序。
echo.
pause

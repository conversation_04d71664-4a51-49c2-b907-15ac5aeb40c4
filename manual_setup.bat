@echo off
chcp 65001 > nul
echo 手动设置PDF编辑工具环境...

REM 检查Python是否已安装
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python未安装！请先安装Python 3.8或更高版本。
    echo 可以从 https://www.python.org/downloads/ 下载Python。
    pause
    exit /b 1
)

REM 检查pip是否已安装
python -m pip --version > nul 2>&1
if %errorlevel% neq 0 (
    echo pip未安装！请先安装pip。
    pause
    exit /b 1
)

REM 检查virtualenv是否已安装
python -m pip show virtualenv > nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装virtualenv...
    python -m pip install virtualenv
)

REM 创建虚拟环境
if not exist venv (
    echo 正在创建虚拟环境...
    python -m virtualenv venv
) else (
    echo 虚拟环境已存在，跳过创建步骤。
)

REM 激活虚拟环境并安装依赖
echo 正在安装依赖项...
call venv\Scripts\activate.bat
python -m pip install -r requirements.txt

REM 手动安装Poppler
echo.
echo =====================================================================
echo 请手动下载并安装Poppler:
echo 1. 下载Poppler: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
echo 2. 解压下载的文件
echo 3. 将解压后的文件夹重命名为"poppler"并放在当前目录中
echo =====================================================================
echo.

set /p answer=是否已完成Poppler的下载和安装? (y/n): 
if /i "%answer%" neq "y" (
    echo 请完成Poppler的安装后再运行应用程序。
    pause
    exit /b 1
)

REM 检查Poppler是否存在
if not exist poppler\Library\bin (
    echo 未检测到正确的Poppler安装。请确保:
    echo 1. 已下载并解压Poppler
    echo 2. 解压后的文件夹重命名为"poppler"
    echo 3. "poppler"文件夹放在当前目录中
    echo 4. 文件夹结构正确(应包含Library\bin子目录)
    pause
    exit /b 1
)

REM 创建uploads和processed目录
echo 正在创建必要的目录...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

echo.
echo 设置完成！
echo 使用 run.bat 启动应用程序。
echo.
pause

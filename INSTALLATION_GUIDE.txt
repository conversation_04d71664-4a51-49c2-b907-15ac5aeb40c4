PDF EDITOR - INSTALLATION GUIDE
==============================

This guide provides multiple methods to install the PDF Editor application.
Choose the method that works best for your system.

PREREQUISITES
------------
- Windows 7/8/10/11
- Python 3.8 or higher (https://www.python.org/downloads/)
- Internet connection (for downloading dependencies)

METHOD 1: AUTOMATIC INSTALLATION (ASCII VERSION)
-----------------------------------------------
1. Right-click on setup_ascii.bat and select "Run as administrator"
2. Follow the on-screen instructions
3. When prompted, download and install <PERSON><PERSON> manually
4. After installation is complete, use run_ascii.bat to start the application

METHOD 2: MANUAL INSTALLATION
----------------------------
1. Install Python 3.8 or higher
   - Download from https://www.python.org/downloads/
   - Check "Add Python to PATH" during installation

2. Open Command Prompt (cmd) as administrator
   - Press Windows key + X, then select "Command Prompt (Admin)"

3. Navigate to the application directory
   - Use the cd command, e.g., cd C:\path\to\pdf_editor

4. Install virtualenv
   - Run: pip install virtualenv

5. Create a virtual environment
   - Run: python -m virtualenv venv

6. Activate the virtual environment
   - Run: venv\Scripts\activate.bat

7. Install dependencies
   - Run: pip install -r requirements.txt

8. Download and install Poppler
   - Download from: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
   - Extract the downloaded file
   - Rename the extracted folder to "poppler"
   - Place the "poppler" folder in the application directory

9. Create necessary directories
   - Run: mkdir uploads
   - Run: mkdir processed

10. Start the application
    - Run: python app.py

METHOD 3: POWERSHELL INSTALLATION
--------------------------------
If you're comfortable with PowerShell, you can use this method:

1. Right-click on download_poppler.ps1 and select "Run with PowerShell"
   - If you get a security warning, press "R" to run once
   - Or open PowerShell as administrator and run:
     Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass

2. Then follow steps 1-7 and 9-10 from Method 2

TROUBLESHOOTING
--------------

1. "Python is not recognized as an internal or external command"
   - Make sure Python is installed and added to PATH
   - Restart your computer after installing Python

2. PowerShell execution policy restrictions
   - Open PowerShell as administrator
   - Run: Set-ExecutionPolicy RemoteSigned
   - Select "Y" to confirm

3. Download failures
   - Use a web browser to download files manually
   - Make sure your internet connection is stable
   - Check if your firewall or antivirus is blocking downloads

4. "poppler\Library\bin" not found
   - Make sure you've downloaded the correct Poppler version
   - Extract the ZIP file completely
   - Rename the folder to "poppler" (all lowercase)
   - Place it in the same directory as the application files

5. Application crashes or errors
   - Make sure all dependencies are installed correctly
   - Check if Python version is 3.8 or higher
   - Make sure Poppler is installed correctly

RUNNING THE APPLICATION
---------------------
After successful installation:

1. Double-click run_ascii.bat
   - Or activate the virtual environment and run python app.py

2. Open a web browser and navigate to:
   - http://127.0.0.1:5001 (local access)
   - http://[your-ip-address]:5001 (network access)

TECHNICAL SUPPORT
---------------
If you encounter any issues not covered in this guide, please contact technical support.

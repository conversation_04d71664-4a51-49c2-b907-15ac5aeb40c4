#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PDF类型检测问题
"""

import os
import sys
import logging
import comprehensive_processor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def debug_pdf_type_detection():
    """调试PDF类型检测"""
    print("="*60)
    print("调试PDF类型检测")
    print("="*60)
    
    # 查找当前目录下的PDF文件
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return
    
    for pdf_file in pdf_files[:5]:  # 只检查前5个文件
        print(f"\n检查文件: {pdf_file}")
        
        try:
            # 使用现有的检测方法
            is_scanned = comprehensive_processor.is_scanned_pdf(pdf_file)
            print(f"当前检测结果: {'扫描型' if is_scanned else '文本型'}")
            
            # 详细分析
            import fitz
            doc = fitz.open(pdf_file)
            
            total_text_length = 0
            page_count = min(2, len(doc))
            
            for page_num in range(page_count):
                page = doc[page_num]
                text = page.get_text()
                text_length = len(text.strip())
                total_text_length += text_length
                print(f"  页面{page_num+1}文本长度: {text_length}")
                
                # 检查是否有图像
                images = page.get_images()
                print(f"  页面{page_num+1}图像数量: {len(images)}")
                
                # 显示前100个字符
                if text.strip():
                    preview = text.strip()[:100].replace('\n', ' ')
                    print(f"  文本预览: {preview}...")
            
            avg_text_length = total_text_length / page_count if page_count > 0 else 0
            print(f"平均文本长度: {avg_text_length:.1f}")
            print(f"判断标准: 平均文本长度 < 100 = 扫描型")
            
            doc.close()
            
        except Exception as e:
            print(f"检查文件时出错: {str(e)}")

def test_improved_detection():
    """测试改进的PDF类型检测"""
    print("\n" + "="*60)
    print("测试改进的PDF类型检测方法")
    print("="*60)
    
    def improved_is_scanned_pdf(input_path):
        """改进的PDF类型检测"""
        try:
            import fitz
            doc = fitz.open(input_path)
            page_count = min(3, len(doc))  # 检查前3页
            
            text_pages = 0
            image_pages = 0
            total_text_length = 0
            
            for page_num in range(page_count):
                page = doc[page_num]
                text = page.get_text()
                text_length = len(text.strip())
                total_text_length += text_length
                
                # 检查图像
                images = page.get_images()
                
                # 如果页面有大量文本，认为是文本页
                if text_length > 200:
                    text_pages += 1
                
                # 如果页面有大图像且文本很少，认为是图像页
                if len(images) > 0 and text_length < 50:
                    image_pages += 1
            
            doc.close()
            
            # 综合判断
            avg_text_length = total_text_length / page_count if page_count > 0 else 0
            
            # 如果大部分页面是图像页，或者平均文本很少，认为是扫描型
            if image_pages > text_pages or avg_text_length < 100:
                return True
            else:
                return False
                
        except Exception as e:
            logging.error(f"检测PDF类型时出错: {str(e)}")
            return True  # 出错时默认为扫描型
    
    # 查找PDF文件
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    for pdf_file in pdf_files[:5]:
        print(f"\n文件: {pdf_file}")
        
        try:
            old_result = comprehensive_processor.is_scanned_pdf(pdf_file)
            new_result = improved_is_scanned_pdf(pdf_file)
            
            print(f"原方法: {'扫描型' if old_result else '文本型'}")
            print(f"新方法: {'扫描型' if new_result else '文本型'}")
            
            if old_result != new_result:
                print("⚠️  检测结果不一致!")
            else:
                print("✓ 检测结果一致")
                
        except Exception as e:
            print(f"测试时出错: {str(e)}")

if __name__ == "__main__":
    debug_pdf_type_detection()
    test_improved_detection()

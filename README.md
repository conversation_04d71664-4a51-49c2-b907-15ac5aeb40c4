# PDF编辑工具

这是一个用于处理PDF文件的工具，提供以下功能：

- 去除红头
- 强制去除红头
- 去除背景
- 转换为Word
- 局域网访问支持

## 系统要求

- Windows 7/8/10/11
- Python 3.8或更高版本
- 至少2GB可用内存
- 至少500MB可用磁盘空间

## 安装步骤

1. 确保已安装Python 3.8或更高版本
   - 可以从[Python官网](https://www.python.org/downloads/)下载安装
   - 安装时请勾选"Add Python to PATH"选项

2. 下载本应用程序的所有文件

3. 双击运行`setup_venv.bat`脚本
   - 这将自动设置虚拟环境并安装所有依赖项
   - 首次运行可能需要几分钟时间

## 启动应用程序

1. 双击运行`run.bat`脚本
   - 应用程序将在命令行窗口中启动
   - 窗口中会显示本地访问地址和局域网访问地址

2. 在浏览器中访问显示的地址
   - 本地访问：http://127.0.0.1:5001
   - 局域网访问：http://[您的IP地址]:5001

## 使用方法

1. 在网页界面上传PDF文件
   - 可以拖放文件到上传区域
   - 也可以点击上传区域选择文件

2. 选择要执行的操作
   - 预览：查看PDF文件内容
   - 去红头：去除文档中的红色标题
   - 强制去红头：将文本型PDF转为图像型后去除红头
   - 去背景：去除文档背景杂色，增强文字清晰度
   - 转为Word：将PDF转换为Word文档

3. 处理完成后，文件将自动下载

## 注意事项

- 应用程序会自动清理临时文件
- 上传的文件和处理后的文件会在2小时后自动删除
- 可以通过页面底部的"清理临时文件"按钮手动清理

## 常见问题

1. **应用程序无法启动**
   - 确保已正确安装Python
   - 尝试重新运行setup_venv.bat

2. **PDF处理失败**
   - 确保上传的是有效的PDF文件
   - 对于复杂的PDF，尝试使用"强制去红头"选项

3. **无法在局域网中访问**
   - 确保防火墙允许5001端口的访问
   - 检查电脑是否连接到网络

## 技术支持

如有问题，请联系技术支持。

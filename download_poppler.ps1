# PowerShell script to download and install Poppler

# Set progress preference to silent to speed up download
$ProgressPreference = 'SilentlyContinue'

# Define download URL and output file
$url = "https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip"
$output = "poppler.zip"

Write-Host "Downloading Poppler..."
try {
    # Download the file
    Invoke-WebRequest -Uri $url -OutFile $output
    
    # Check if download was successful
    if (Test-Path $output) {
        Write-Host "Download successful."
        
        # Create poppler directory if it doesn't exist
        if (-not (Test-Path "poppler")) {
            New-Item -ItemType Directory -Path "poppler" | Out-Null
        }
        
        Write-Host "Extracting Poppler..."
        # Extract the zip file
        Expand-Archive -Path $output -DestinationPath "poppler" -Force
        
        # Remove the zip file
        Remove-Item $output
        
        # Check if extraction was successful
        if (Test-Path "poppler\Library\bin") {
            Write-Host "Poppler installation complete."
        } else {
            Write-Host "Error: Poppler extraction failed or directory structure is incorrect."
            exit 1
        }
    } else {
        Write-Host "Error: Download failed."
        exit 1
    }
} catch {
    Write-Host "Error: $_"
    exit 1
}

Write-Host "Poppler has been successfully installed."

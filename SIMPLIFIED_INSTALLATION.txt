PDF EDITOR - <PERSON><PERSON><PERSON><PERSON>IED INSTALLATION GUIDE
=======================================

If you're experiencing installation issues, please follow this simplified guide.

PREREQUISITES
------------
- Windows 7/8/10/11
- Python 3.8 or higher (https://www.python.org/downloads/)
- Internet connection (for downloading dependencies)

SIMPLIFIED INSTALLATION (RECOMMENDED)
-----------------------------------
1. Right-click on easy_install.bat and select "Run as administrator"
2. Follow the on-screen instructions
3. When prompted, download and install Pop<PERSON> manually
4. After installation is complete, use run_ascii.bat to start the application

MANUAL POPPLER INSTALLATION
--------------------------
If you need to install Pop<PERSON> manually:

1. Download Poppler:
   https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip

2. Extract the ZIP file to a temporary location

3. Rename the extracted folder to "poppler" (all lowercase)

4. Move the "poppler" folder to the same directory as the application files

5. Verify that the folder structure is correct:
   - The path "poppler\Library\bin" should exist
   - The bin folder should contain files like "pdfinfo.exe", "pdftoppm.exe", etc.

TROUBLESHOOTING
--------------

1. "Cannot import 'setuptools.build_meta'" or compilation errors:
   - Use the easy_install.bat script which avoids compilation
   - Or install Visual C++ Build Tools from:
     https://visualstudio.microsoft.com/visual-cpp-build-tools/

2. "Python is not recognized as an internal or external command":
   - Make sure Python is installed and added to PATH
   - Restart your computer after installing Python

3. "No module named 'venv'":
   - Install Python with the "pip" option checked
   - Or run: python -m pip install virtualenv

4. "poppler\Library\bin" not found:
   - Make sure you've downloaded the correct Poppler version
   - Extract the ZIP file completely
   - Rename the folder to "poppler" (all lowercase)
   - Place it in the same directory as the application files

RUNNING THE APPLICATION
---------------------
After successful installation:

1. Double-click run_ascii.bat
   - Or activate the virtual environment and run python app.py

2. Open a web browser and navigate to:
   - http://127.0.0.1:5001 (local access)
   - http://[your-ip-address]:5001 (network access)

TECHNICAL SUPPORT
---------------
If you encounter any issues not covered in this guide, please contact technical support.

PDF编辑工具安装指南
====================

如果自动安装脚本(setup_venv.bat)运行失败，请按照以下步骤手动安装:

1. 安装Python
--------------
- 下载并安装Python 3.8或更高版本: https://www.python.org/downloads/
- 安装时请勾选"Add Python to PATH"选项
- 验证安装: 打开命令提示符(cmd)，输入 python --version

2. 创建虚拟环境
--------------
- 打开命令提示符(cmd)，进入应用程序目录
- 安装virtualenv: pip install virtualenv
- 创建虚拟环境: python -m virtualenv venv
- 激活虚拟环境: venv\Scripts\activate.bat

3. 安装依赖项
--------------
- 确保虚拟环境已激活(命令提示符前应显示(venv))
- 安装依赖: pip install -r requirements.txt

4. 安装Poppler
--------------
- 下载Poppler: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
- 解压下载的文件
- 将解压后的文件夹重命名为"poppler"
- 将"poppler"文件夹放在应用程序目录中
- 确认文件夹结构正确(应包含Library\bin子目录)

5. 创建必要的目录
--------------
- 在应用程序目录中创建"uploads"文件夹
- 在应用程序目录中创建"processed"文件夹

6. 运行应用程序
--------------
- 双击运行run.bat脚本
- 或者在命令提示符中运行: venv\Scripts\activate.bat && python app.py

常见问题
====================

1. PowerShell执行策略限制
--------------
如果遇到PowerShell执行策略限制，可以尝试以下方法:
- 以管理员身份打开PowerShell
- 执行: Set-ExecutionPolicy RemoteSigned
- 选择"Y"确认更改

2. 下载Poppler失败
--------------
如果自动下载Poppler失败，请使用浏览器手动下载:
- 下载地址: https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
- 然后按照上面的"安装Poppler"步骤操作

3. 中文乱码问题
--------------
如果遇到中文乱码问题，请确保:
- 系统默认编码为UTF-8
- 或者使用manual_setup.bat脚本，它会自动设置正确的编码

4. 其他问题
--------------
如果遇到其他问题，请尝试使用manual_setup.bat脚本，它提供了更详细的安装步骤和错误提示。

技术支持
====================
如有其他问题，请联系技术支持。

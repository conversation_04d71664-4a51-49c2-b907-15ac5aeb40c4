from flask import Flask, render_template, request, redirect, url_for, send_from_directory, session, flash, Response
import os
import shutil
import logging
from werkzeug.utils import secure_filename
import re
import time
import datetime

import base64

def safe_filename(filename):
    """安全文件名处理函数，完整保留原始文件名信息"""
    # 提取文件名（不含路径）
    basename = os.path.basename(filename)
    # 使用base64编码保存完整文件名
    encoded = base64.urlsafe_b64encode(basename.encode('utf-8')).decode('ascii')
    return encoded
import pdfplumber
import cv2
import numpy as np
from PIL import Image
from PyPDF2 import PdfReader, PdfWriter
from pdfminer.high_level import extract_text
from pdf2image import convert_from_path
import zipfile
from io import BytesIO
from reportlab.pdfgen import canvas
from reportlab.lib.colors import white

# 配置日志
logging.basicConfig(
    level=logging.ERROR,
    format='%(levelname)s: %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__, static_url_path='/static')
app.config['UPLOAD_FOLDER'] = 'temp/uploads'
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用缓存
app.config['PROCESSED_FOLDER'] = 'temp/processed'
app.config['MAX_CONTENT_LENGTH'] = 60 * 1024 * 1024  # 60MB限制
app.secret_key = 'development-key'  # 生产环境应使用更安全的密钥
app.config['DEBUG'] = False
app.config['PROPAGATE_EXCEPTIONS'] = False
app.config['TEMPLATES_AUTO_RELOAD'] = False

# 确保上传和处理目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['PROCESSED_FOLDER'], exist_ok=True)

def cleanup_temp_files(max_age_days=1):
    """清理临时文件夹中超过指定天数的文件"""
    try:
        
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60
        
        # 清理上传目录
        cleanup_dir(app.config['UPLOAD_FOLDER'], current_time, max_age_seconds)
        
        # 清理处理目录
        cleanup_dir(app.config['PROCESSED_FOLDER'], current_time, max_age_seconds)
        
        
    except Exception as e:
        logger.error(f"清理临时文件时出错: {e}", exc_info=True)

def cleanup_dir(directory, current_time, max_age_seconds):
    """清理指定目录中的过期文件"""
    try:
        if not os.path.exists(directory):
            return
            
        count = 0
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if os.path.isfile(filepath):
                file_age = current_time - os.path.getmtime(filepath)
                if file_age > max_age_seconds:
                    os.remove(filepath)
                    count += 1
                    
        
        
    except Exception as e:
        logger.error(f"清理目录 {directory} 时出错: {e}")

# 应用启动时清理临时文件
cleanup_temp_files()

def is_valid_pdf(filepath):
    """验证PDF文件是否有效"""
    try:
        # 检查文件头是否符合PDF标准
        with open(filepath, 'rb') as f:
            header = f.read(5)
            if header != b'%PDF-':
                logger.error(f"无效的PDF文件头: {header}")
                return False
        
        # 检查文件大小是否合理
        if os.path.getsize(filepath) < 100:  # 最小PDF文件大小
            logger.error("文件大小异常")
            return False
            
        # 尝试解析PDF结构
        with open(filepath, 'rb') as f:
            reader = PdfReader(f)
            if len(reader.pages) == 0:
                logger.error("PDF没有有效页面")
                return False
                
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "No /Root object" in error_msg:
            logger.error("PDF结构损坏，缺少根对象")
        elif "startxref" in error_msg:
            logger.error("PDF交叉引用表损坏")
        elif "EOF marker" in error_msg:
            logger.error("PDF文件不完整")
        else:
            logger.error(f"验证PDF时出错: {e}")
        return False

def is_scanned_pdf(filepath):
    """检测PDF是否为扫描件"""
    try:
        with pdfplumber.open(filepath) as pdf:
            # 检查前3页是否有文本
            for i, page in enumerate(pdf.pages[:3]):
                text = page.extract_text(strip=True)
                if text:
                    
                    return False
        
        return True
    except Exception as e:
        logger.error(f"检测PDF类型时出错: {e}")
        return False

def detect_red_regions(image):
    """检测图像中的红色区域"""
    try:
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 更精确的红色公章识别参数
        lower_red1 = np.array([0, 150, 150])  # 提高饱和度阈值以减少误检
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 150, 150])  # 提高饱和度阈值
        upper_red2 = np.array([180, 255, 255])
        
        # 创建红色区域的掩膜
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # 增强形态学操作参数
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15,15))
        red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=5)
        
        # 找到轮廓
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤掉太小的区域
        min_area = 100
        contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
        
        return contours
    except Exception as e:
        logger.error(f"检测红色区域时出错: {e}")
        return []

def fill_seal_region(image, x, y, w, h):
    """填充公章区域，保留黑色文字"""
    try:
        # 增加填充条高度到30像素(约2mm)并扩大填充区域
        strip_height = 30
        
        # 扩大公章区域范围
        padding = int(min(w,h)*0.3)
        x = max(0, x - padding)
        y = max(0, y - padding)
        w = min(image.shape[1] - x, w + 2*padding)
        h = min(image.shape[0] - y, h + 2*padding)
        
        # 将公章区域转换为HSV色彩空间
        seal_region = image[y:y+h, x:x+w]
        hsv = cv2.cvtColor(seal_region, cv2.COLOR_BGR2HSV)
        
        # 优化红色区域检测参数
        lower_red1 = np.array([0, 200, 200])  # 进一步提高饱和度阈值
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 200, 200])  # 进一步提高饱和度阈值
        upper_red2 = np.array([180, 255, 255])
        
        # 调整黑色区域检测参数
        lower_black = np.array([0, 0, 0])
        upper_black = np.array([180, 255, 50])  # 降低亮度阈值以更好保留文字
        
        # 按strip_height高度分区处理
        for strip_y in range(0, h, strip_height):
            strip_end = min(strip_y + strip_height, h)
            strip = seal_region[strip_y:strip_end, :]
            
            # 检测红色区域
            hsv_strip = cv2.cvtColor(strip, cv2.COLOR_BGR2HSV)
            red_mask1 = cv2.inRange(hsv_strip, lower_red1, upper_red1)
            red_mask2 = cv2.inRange(hsv_strip, lower_red2, upper_red2)
            red_mask = cv2.bitwise_or(red_mask1, red_mask2)
            
            # 形态学操作增强红色区域检测
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
            red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
            
            # 检测黑色区域
            black_mask = cv2.inRange(hsv_strip, lower_black, upper_black)
            
            # 只填充纯红色区域(没有黑色像素的区域)
            fill_mask = cv2.bitwise_and(red_mask, cv2.bitwise_not(black_mask))
            
            # 应用填充 - 增加填充强度
            strip[fill_mask > 0] = (255, 255, 255)
            
            # 二次填充确保完全覆盖
            if np.any(fill_mask):
                strip[cv2.dilate(fill_mask, kernel, iterations=1) > 0] = (255, 255, 255)
            
        return True
    except Exception as e:
        logger.error(f"填充公章区域时出错: {e}")
        return False

def process_scanned_pdf(input_path, output_path):
    """处理扫描型PDF - 精确识别公章区域内的黑色文字行，只在空白区域填充白色长条"""
    try:
        # 将PDF转换为图像列表
        images = convert_from_path(input_path, dpi=300)
        
        processed_images = []
        for i, img in enumerate(images):
            # 转换为OpenCV格式
            opencv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # 检测红色区域（增强红头检测）
            hsv = cv2.cvtColor(opencv_img, cv2.COLOR_BGR2HSV)
            
            # 更精确的红头文字检测参数
            lower_red1 = np.array([0, 150, 150])  # 亮红色
            upper_red1 = np.array([10, 255, 255])
            lower_red2 = np.array([160, 150, 150])  # 暗红色
            upper_red2 = np.array([180, 255, 255])
            
            # 创建红色区域掩膜
            mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
            mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
            red_mask = cv2.bitwise_or(mask1, mask2)
            
            # 增强顶部区域的红头检测（顶部70%区域）
            height = opencv_img.shape[0]
            top_region_mask = np.zeros_like(red_mask)
            top_region_mask[:int(height*0.7), :] = 255
            red_mask = cv2.bitwise_and(red_mask, top_region_mask)
            
            # 形态学操作增强红头区域
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5,5))
            red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
            
            # 找到红头轮廓
            red_contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 处理红头区域（完全遮盖）
            for cnt in red_contours:
                x, y, w, h = cv2.boundingRect(cnt)
    
                # 只处理顶部区域的红头（y坐标在图像顶部70%内）
                if y < opencv_img.shape[0]*0.7:
                    # 检查是否为圆形区域（可能为公章）
                    aspect_ratio = float(w)/h
                    circularity = 4 * 3.1416 * cv2.contourArea(cnt) / (cv2.arcLength(cnt, True)**2)
                    
                    # 处理公章区域（分区填充）
                    if aspect_ratio > 0.8 and aspect_ratio < 1.2 and circularity > 0.7:
                        fill_seal_region(opencv_img, x, y, w, h)
                        continue
                        
                    # 完全遮盖整个红头行区域
                    padding = max(20, int(min(w,h)*0.2))  # 更大的边距确保完全覆盖
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(opencv_img.shape[1] - x, w + 2*padding)
                    h = min(opencv_img.shape[0] - y, h + padding)  # 主要向下扩展
    
                    # 填充纯白色
                    cv2.rectangle(opencv_img, (x, y), (x+w, y+h), (255, 255, 255), -1)
            
            # 转换回PIL格式
            processed_images.append(Image.fromarray(cv2.cvtColor(opencv_img, cv2.COLOR_BGR2RGB)))
        
        # 保存为PDF
        if processed_images:
            processed_images[0].save(
                output_path, "PDF", 
                resolution=300, 
                save_all=True, 
                append_images=processed_images[1:]
            )
            return True
        return False
        
    except Exception as e:
        logger.error(f"处理扫描型PDF时出错: {e}", exc_info=True)
        return False

def process_text_pdf(input_path, output_path):
    """处理文本型PDF - 识别并覆盖红头文字和公章"""
    try:
        logger.info("开始处理文本型PDF...")
        
        # 使用PyPDF2读取PDF
        reader = PdfReader(input_path)
        # 创建PDF写入器
        writer = PdfWriter()
        
        # 使用BytesIO对象用于临时存储PDF内容
        
        with pdfplumber.open(input_path) as pdf:
            
            for i, page in enumerate(pdf.pages):
                logger.info(f"正在处理第{i+1}页")
                
                # 获取页面文本对象
                chars = page.chars
                words = page.extract_words()
                
                # 获取原始PDF页面对象
                original_page = reader.pages[i]
                
                # 使用pdfplumber分析页面内容
                pdf_page = pdf.pages[i]
                
                # 创建一个临时的PDF页面，用于绘制白色矩形
                page_width = float(pdf_page.width)
                page_height = float(pdf_page.height)
                packet = BytesIO()
                can = canvas.Canvas(packet, pagesize=(page_width, page_height))
                can.setFillColor(white)  # 设置填充颜色为白色
                
                # 需要覆盖的区域列表
                areas_to_cover = []
                
                # 改进的红头文字识别方法
                # 1. 检查顶部区域的红色文字
                top_margin = page.height * 0.8  # 扩大到顶部80%区域
                red_text_chars = [char for char in chars 
                               if char['top'] < top_margin 
                               and isinstance(char.get('non_stroking_color'), tuple) 
                               and len(char.get('non_stroking_color', ())) >= 3 
                               and char['non_stroking_color'][0] > 0.7  # 红色分量高
                               and char['non_stroking_color'][1] < 0.3  # 绿色分量低
                               and char['non_stroking_color'][2] < 0.3]  # 蓝色分量低
                
                # 2. 检查整个页面的红色文字块（可能是标题或重要内容）
                red_text_blocks = []
                if words:
                    # 按行分组文字
                    lines = {}
                    for word in words:
                        line_key = int(word['top'] / 10) * 10  # 按10pt高度分组
                        if line_key not in lines:
                            lines[line_key] = []
                        lines[line_key].append(word)
                    
                    # 检查每行是否包含红色文字
                    for line_key, line_words in lines.items():
                        line_chars = [char for char in chars if int(char['top'] / 10) * 10 == line_key]
                        red_chars_count = sum(1 for char in line_chars 
                                           if isinstance(char.get('non_stroking_color'), tuple) 
                                           and len(char.get('non_stroking_color', ())) >= 3 
                                           and char['non_stroking_color'][0] > 0.7 
                                           and char['non_stroking_color'][1] < 0.3 
                                           and char['non_stroking_color'][2] < 0.3)
                        
                        # 如果一行中超过50%的字符是红色的，认为是红头文字
                        if line_chars and red_chars_count / len(line_chars) > 0.5:
                            # 获取这一行的边界
                            if line_chars:
                                x0 = min(char['x0'] for char in line_chars) - 5
                                x1 = max(char['x1'] for char in line_chars) + 5
                                y0 = min(char['top'] for char in line_chars) - 5
                                y1 = max(char['bottom'] for char in line_chars) + 5
                                
                                # 确保坐标在页面范围内
                                x0 = max(0, x0)
                                y0 = max(0, y0)
                                x1 = min(pdf_page.width, x1)
                                y1 = min(pdf_page.height, y1)
                                
                                red_text_blocks.append({
                                    'x0': x0, 'y0': y0, 'x1': x1, 'y1': y1,
                                    'text': ' '.join(word['text'] for word in line_words)
                                })
                
                # 处理顶部红头文字区域
                if red_text_chars:
                    logger.info(f"发现 {len(red_text_chars)} 个红头文字字符")
                    
                    # 获取红头文字的边界框（更宽松的边界）
                    x0 = min(char['x0'] for char in red_text_chars) - 15  # 增加边距
                    x1 = max(char['x1'] for char in red_text_chars) + 15
                    y0 = min(char['top'] for char in red_text_chars) - 15
                    y1 = max(char['bottom'] for char in red_text_chars) + 15
                    
                    logger.info(f"红头区域坐标: x0={x0}, y0={y0}, x1={x1}, y1={y1}")
                    
                    # 确保坐标在页面范围内
                    x0 = max(0, x0)
                    y0 = max(0, y0)
                    x1 = min(pdf_page.width, x1)
                    y1 = min(pdf_page.height, y1)
                    
                    # 添加到需要覆盖的区域列表
                    areas_to_cover.append((x0, y0, x1, y1))
                
                # 处理红色文字块
                for block in red_text_blocks:
                    logger.info(f"处理红色文字块: {block['text'][:30]}...")
                    
                    # 扩大边界以确保完全覆盖
                    x0 = block['x0'] - 5
                    y0 = block['y0'] - 5
                    x1 = block['x1'] + 5
                    y1 = block['y1'] + 5
                    
                    # 确保坐标在页面范围内
                    x0 = max(0, x0)
                    y0 = max(0, y0)
                    x1 = min(pdf_page.width, x1)
                    y1 = min(pdf_page.height, y1)
                    
                    # 添加到需要覆盖的区域列表
                    areas_to_cover.append((x0, y0, x1, y1))
                
                # 识别公章（红色圆形区域）
                red_contours = []
                if page.images:
                    img = page.to_image(resolution=300).original
                    red_contours = detect_red_regions(np.array(img))
                
                # 处理公章区域
                for cnt in red_contours:
                    x, y, w, h = cv2.boundingRect(cnt)
                    # 转换为PDF坐标
                    pdf_x = x * 72 / 300  # 假设300dpi
                    pdf_y = page_height - (y + h) * 72 / 300  # 注意PDF坐标系与图像坐标系的y轴方向相反
                    pdf_w = w * 72 / 300
                    pdf_h = h * 72 / 300
                    
                    # 扩大边界以确保完全覆盖
                    padding = 5 * 72 / 300  # 5像素的边距转换为PDF单位
                    pdf_x -= padding
                    pdf_y -= padding
                    pdf_w += 2 * padding
                    pdf_h += 2 * padding
                    
                    # 确保坐标在页面范围内
                    pdf_x = max(0, pdf_x)
                    pdf_y = max(0, pdf_y)
                    pdf_x_max = min(page_width, pdf_x + pdf_w)
                    pdf_y_max = min(page_height, pdf_y + pdf_h)
                    
                    # 添加到需要覆盖的区域列表 - 使用pdfplumber坐标系格式，以便后续统一处理
                    # 将PDF坐标系转换回pdfplumber坐标系，设置y0>y1表示这是公章区域
                    y_top = page_height - pdf_y_max
                    y_bottom = page_height - pdf_y
                    areas_to_cover.append((pdf_x, y_bottom, pdf_x_max, y_top))
                
                # 在临时PDF上绘制所有白色矩形
                for area in areas_to_cover:
                    x0, y0, x1, y1 = area
                    # 在ReportLab中，坐标系原点在左下角
                    # 而pdfplumber中，坐标系原点在左上角，需要转换y坐标
                    
                    # 所有坐标都已转换为pdfplumber格式，统一处理
                    # 将pdfplumber坐标系转换为ReportLab坐标系
                    rl_y0 = page_height - y1  # 转换y坐标
                    rl_y1 = page_height - y0
                    width = x1 - x0
                    height = rl_y1 - rl_y0
                    
                    # 绘制白色矩形
                    can.rect(x0, rl_y0, width, height, fill=1, stroke=0)
                    logger.info(f"绘制白色矩形: x={x0}, y={rl_y0}, width={width}, height={height}")
                    
                    # 为确保完全覆盖，再绘制一个稍小的矩形
                    padding = 2
                    if width > 2*padding and height > 2*padding:  # 确保矩形足够大
                        can.rect(x0+padding, rl_y0+padding, width-2*padding, height-2*padding, fill=1, stroke=0)
                
                # 保存临时PDF
                can.save()
                packet.seek(0)
                
                # 创建一个新的PDF，包含白色矩形
                overlay_pdf = PdfReader(packet)
                overlay_page = overlay_pdf.pages[0]
                
                # 将白色矩形覆盖到原始页面上
                original_page.merge_page(overlay_page)
                
                # 处理PDF注释（安全方式）
                try:
                    if hasattr(original_page, 'get') and '/Annots' in original_page:
                        annotations = original_page['/Annots']
                        if annotations:
                            # 创建副本避免修改迭代中的对象
                            for annot_ref in list(annotations):
                                try:
                                    annot = annot_ref.get_object()
                                    if annot.get('/Subtype') == '/Stamp':
                                        annotations.remove(annot_ref)
                                except Exception as e:
                                    logger.warning(f"处理注释时出错: {e}")
                                    continue
                except Exception as e:
                    logger.error(f"处理PDF注释时出错: {e}")
                
                # 添加处理后的页面到输出PDF
                writer.add_page(original_page)
            
            # 应用修改并保存
            with open(output_path, 'wb') as fout:
                writer.write(fout)
            
            # 强制刷新PDF元数据以确保所有修改生效
            with open(output_path, 'rb') as fin:
                pdf_reader = PdfReader(fin)
                pdf_writer = PdfWriter()
                
                for page in pdf_reader.pages:
                    # 确保移除所有注释，包括隐藏的
                    if '/Annots' in page:
                        del page['/Annots']
                    
                    # 确保移除所有图层
                    if '/OCProperties' in page:
                        del page['/OCProperties']
                    
                    pdf_writer.add_page(page)
                
                with open(output_path, 'wb') as fout:
                    pdf_writer.write(fout)
            
            # 对第一页进行复查，检查可能遗漏的红头
            try:
                # 将第一页转换为图片
                first_page_image = convert_from_path(output_path, first_page=1, last_page=1, dpi=300)[0]
                opencv_img = cv2.cvtColor(np.array(first_page_image), cv2.COLOR_RGB2BGR)
                
                # 检测顶部红色区域（检查顶部80%的区域）
                height = opencv_img.shape[0]
                top_region = opencv_img[0:int(height*0.8), :]
                red_contours = detect_red_regions(top_region)
                
                # 如果检测到红色区域或原PDF是文本型但顶部区域颜色异常
                if red_contours or (not is_scanned_pdf(input_path) and 
                                  np.mean(top_region[:,:,0]) > 150 and 
                                  np.mean(top_region[:,:,1]) < 100 and 
                                  np.mean(top_region[:,:,2]) < 100):
                    logger.info("复查发现第一页顶部有红色区域或异常颜色，进行遮盖处理")
                    
                    # 仅遮盖检测到的红色轮廓区域
                    for cnt in red_contours:
                        x, y, w, h = cv2.boundingRect(cnt)
                        # 扩大边界以确保完全覆盖
                        padding = 5
                        x = max(0, x - padding)
                        y = max(0, y - padding)
                        w = min(opencv_img.shape[1] - x, w + 2*padding)
                        h = min(int(height*0.8) - y, h + 2*padding)
                        cv2.rectangle(opencv_img, (x, y), (x+w, y+h), (255, 255, 255), -1)
                    
                    # 转换回PIL格式并保存为临时PDF
                    processed_img = Image.fromarray(cv2.cvtColor(opencv_img, cv2.COLOR_BGR2RGB))
                    temp_pdf_path = output_path + ".temp.pdf"
                    processed_img.save(temp_pdf_path, "PDF", resolution=300)
                    
                    # 合并处理后的第一页和原始PDF的其他页
                    temp_reader = PdfReader(temp_pdf_path)
                    output_reader = PdfReader(output_path)
                    final_writer = PdfWriter()
                    
                    # 添加处理后的第一页
                    final_writer.add_page(temp_reader.pages[0])
                    
                    # 添加原始PDF的其他页
                    if len(output_reader.pages) > 1:
                        for page in output_reader.pages[1:]:
                            final_writer.add_page(page)
                    
                    # 覆盖原输出文件
                    with open(output_path, 'wb') as fout:
                        final_writer.write(fout)
                    
                    # 删除临时文件
                    os.remove(temp_pdf_path)
            except Exception as e:
                logger.error(f"复查第一页时出错: {e}", exc_info=True)
            
            logger.info("文本型PDF处理成功")
            return True
            
    except Exception as e:
        logger.error(f"处理文本型PDF时出错: {e}", exc_info=True)
        return False

def process_pdf(input_path, output_path):
    """主处理函数"""
    try:
        logger.info(f"开始处理文件: {input_path}")
        
        # 尝试文本型PDF处理
        if not is_scanned_pdf(input_path):
            logger.info("尝试文本型PDF处理...")
            if process_text_pdf(input_path, output_path):
                logger.info("文本型PDF处理成功")
                return True
        
        # 尝试扫描型PDF处理
        logger.info("尝试扫描型PDF处理...")
        if process_scanned_pdf(input_path, output_path):
            logger.info("扫描型PDF处理成功")
            return True
            
        logger.error("无法确定PDF类型或处理失败")
        return False
        
    except Exception as e:
        logger.error(f"处理PDF时发生严重错误: {str(e)}", exc_info=True)
        return False

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        if 'files' not in request.files:
            flash('没有选择文件', 'error')
            return redirect(request.url)
        
        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            flash('没有选择文件', 'error')
            return redirect(request.url)
        
        processed_files = []
        for file in files:
            if file and allowed_file(file.filename):
                try:
                    filename = file.filename
                    input_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], f'去红头-{filename}')
                    
                    # 保存上传的文件
                    file.save(input_path)
                    
                    
                    # 验证PDF文件有效性（强制处理模式跳过验证）
                    if 'force_process' not in request.form and not is_valid_pdf(input_path):
                        flash(f'文件 {filename} 不是有效的PDF文件或已损坏，请检查后重试', 'error')
                        os.remove(input_path)
                        continue
                    
                    # 处理PDF文件（强制处理模式直接按扫描型处理）
                    if 'force_process' in request.form:
                        if process_scanned_pdf(input_path, output_path):
                            processed_files.append(filename)
                        else:
                            flash(f'文件 {filename} 强制处理失败', 'error')
                    elif process_pdf(input_path, output_path):
                        processed_files.append(filename)
                        
                    else:
                        flash(f'文件 {filename} 处理失败，可能是格式不支持', 'error')
                        logger.error(f"文件 {filename} 处理失败")
                except Exception as e:
                    flash(f'处理文件 {file.filename} 时出错: {str(e)}', 'error')
                    logger.error(f"处理文件时出错: {e}", exc_info=True)
                    if os.path.exists(input_path):
                        os.remove(input_path)
        
        if not processed_files:
            flash('没有文件被成功处理，请检查文件格式', 'error')
            return redirect(request.url)
        
        session['processed_files'] = processed_files
        return redirect(url_for('download'))
    
    return render_template('index.html')

@app.route('/download')
def download():
    if 'processed_files' not in session or not session['processed_files']:
        flash('没有可下载的文件', 'error')
        return redirect(url_for('index'))
    
    processed_files = session['processed_files']
    try:
        if len(processed_files) == 1:
            # 单个文件直接下载
            filename = processed_files[0]
            # 使用安全的文件名处理，避免Latin-1编码错误
            safe_download_name = f'去红头-{filename}'
            response = send_from_directory(
                app.config['PROCESSED_FOLDER'],
                f'去红头-{filename}',
                as_attachment=True,
                download_name=safe_download_name
            )
            
            # 下载完成后清理上传的原始文件
            input_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            if os.path.exists(input_path):
                os.remove(input_path)
                
                
            return response
        else:
            # 多个文件打包下载
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for filename in processed_files:
                    filepath = os.path.join(app.config['PROCESSED_FOLDER'], f'去红头-{filename}')
                    if os.path.exists(filepath):
                        # 使用ASCII兼容的文件名在ZIP内部存储
                        safe_name = f'去红头-{filename}'
                        zipf.write(filepath, safe_name)
            
            zip_buffer.seek(0)
            
            # 清理处理后的文件和上传的原始文件
            for filename in processed_files:
                # 删除处理后的文件
                processed_path = os.path.join(app.config['PROCESSED_FOLDER'], f'去红头-{filename}')
                if os.path.exists(processed_path):
                    os.remove(processed_path)
                    
                
                # 删除上传的原始文件
                input_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                if os.path.exists(input_path):
                    os.remove(input_path)
                    
            
            session.pop('processed_files', None)
            
            # 执行一次临时文件清理
            cleanup_temp_files()
            
            # 直接使用固定的ASCII兼容文件名，避免编码问题
            content_disposition = 'attachment; filename="quhongtou.zip"'
            
            return Response(
                zip_buffer,
                mimetype='application/zip',
                headers={
                    'Content-Disposition': content_disposition,
                    'Content-Type': 'application/zip'
                }
            )
    except Exception as e:
        flash(f'文件下载失败: {str(e)}', 'error')
        logger.error(f"下载失败: {e}", exc_info=True)
        return redirect(url_for('index'))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() == 'pdf'

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
import os
import base64
import zipfile
import logging
import traceback
import threading
import time
import datetime
from flask import Flask, render_template, request, send_file, jsonify
from flask_cors import CORS  # 导入CORS支持
from werkzeug.utils import secure_filename
import ultimate_reliable_processor  # 使用终极可靠的处理模块
import comprehensive_processor  # 使用全面处理模块

# Configure logging - reduced logging level for better performance
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)

app = Flask(__name__)
# 启用CORS支持，允许所有来源的请求
CORS(app)

app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PROCESSED_FOLDER'] = 'processed'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max upload

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['PROCESSED_FOLDER'], exist_ok=True)

def cleanup_directories(max_age_hours=None):
    """
    清理上传和处理目录中的文件

    参数:
        max_age_hours: 如果指定，只删除超过指定小时数的文件；如果为None，删除所有文件
    """
    try:
        now = datetime.datetime.now()
        files_removed = 0

        # 清理上传目录
        for filename in os.listdir(app.config['UPLOAD_FOLDER']):
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            if os.path.isfile(file_path):
                # 如果指定了最大年龄，检查文件修改时间
                if max_age_hours is not None:
                    file_mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                    age_hours = (now - file_mod_time).total_seconds() / 3600
                    if age_hours < max_age_hours:
                        continue  # 跳过未达到最大年龄的文件

                os.unlink(file_path)
                files_removed += 1
                logging.info(f"已删除上传文件: {file_path}")

        # 清理处理目录
        for filename in os.listdir(app.config['PROCESSED_FOLDER']):
            file_path = os.path.join(app.config['PROCESSED_FOLDER'], filename)
            if os.path.isfile(file_path):
                # 如果指定了最大年龄，检查文件修改时间
                if max_age_hours is not None:
                    file_mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path))
                    age_hours = (now - file_mod_time).total_seconds() / 3600
                    if age_hours < max_age_hours:
                        continue  # 跳过未达到最大年龄的文件

                os.unlink(file_path)
                files_removed += 1
                logging.info(f"已删除处理文件: {file_path}")

        logging.info(f"目录清理完成，共删除 {files_removed} 个文件")
    except Exception as e:
        logging.error(f"清理目录时出错: {str(e)}")
        traceback.print_exc()

def scheduled_cleanup():
    """定期清理任务，每小时运行一次，删除超过2小时的文件"""
    while True:
        try:
            # 休眠1小时
            time.sleep(3600)  # 3600秒 = 1小时

            # 执行清理，删除超过2小时的文件
            logging.info("开始定期清理...")
            cleanup_directories(max_age_hours=2)

        except Exception as e:
            logging.error(f"定期清理任务出错: {str(e)}")

# 启动时清理所有文件
logging.info("应用程序启动，清理所有临时文件...")
cleanup_directories()

# 启动定期清理线程
cleanup_thread = threading.Thread(target=scheduled_cleanup, daemon=True)
cleanup_thread.start()
logging.info("定期清理任务已启动，将每小时运行一次，删除超过2小时的文件")

def safe_filename(filename):
    """Create a safe filename that preserves the original name"""
    # Replace problematic characters with underscores
    safe_name = ""
    for char in filename:
        if char.isalnum() or char in ['-', '_', '.', ' ', '(', ')', '[', ']', '（', '）']:
            safe_name += char
        else:
            safe_name += '_'
    return safe_name

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'files[]' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400

    files = request.files.getlist('files[]')
    if not files or files[0].filename == '':
        return jsonify({'error': '没有选择文件'}), 400

    saved_files = []
    for file in files:
        if file and file.filename.lower().endswith('.pdf'):
            # 保留原始文件名，但确保安全
            original_filename = file.filename
            # 使用原始文件名作为安全文件名，但替换不安全字符
            safe_name = safe_filename(original_filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], safe_name)
            file.save(filepath)
            saved_files.append({
                'original_name': original_filename,
                'safe_name': safe_name,
                'path': filepath
            })

    if not saved_files:
        return jsonify({'error': '没有有效的PDF文件'}), 400

    return jsonify({'files': saved_files})

@app.route('/preview', methods=['POST'])
def preview_file():
    try:
        data = request.json
        if not data or 'file' not in data:
            return jsonify({'error': '无效的请求'}), 400

        file_info = data['file']
        input_path = file_info['path']

        if not os.path.exists(input_path):
            return jsonify({'error': '文件不存在'}), 400

        # 生成预览信息
        preview_info = ultimate_reliable_processor.preview_pdf(input_path)

        # 添加文件信息
        preview_info['file_info'] = file_info

        return jsonify(preview_info)

    except Exception as e:
        logging.error(f"Error previewing file: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify({'error': f'预览文件时出错: {str(e)}'}), 500

@app.route('/process', methods=['POST'])
def process_files():
    try:
        data = request.json
        if not data or 'files' not in data or 'action' not in data:
            return jsonify({'error': '无效的请求'}), 400

        files = data['files']
        action = data['action']

        logging.info(f"Processing {len(files)} files with action: {action}")

        processed_files = []

        for file_info in files:
            try:
                input_path = file_info['path']
                original_name = file_info['original_name']
                base_name = os.path.splitext(original_name)[0]

                # Check if input file exists
                if not os.path.exists(input_path):
                    logging.error(f"Input file not found: {input_path}")
                    return jsonify({'error': f'文件不存在: {original_name}'}), 400

                if action == 'remove_header':
                    output_filename = f"去红头-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"去除红头: {input_path} -> {output_path}")

                    # 使用全面处理模块处理所有文件
                    logging.info(f"使用全面处理模块处理文件: {input_path}")
                    comprehensive_processor.process_pdf_comprehensive(input_path, output_path, "remove_red")

                elif action == 'force_process':
                    output_filename = f"强制去红头-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"强制去红头: {input_path} -> {output_path}")

                    # 使用全面处理模块的强制去红头功能
                    logging.info(f"使用全面处理模块强制去红头: {input_path}")
                    comprehensive_processor.process_pdf_comprehensive(input_path, output_path, "force_remove_red")

                elif action == 'remove_background':
                    output_filename = f"已处理-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"去除背景: {input_path} -> {output_path}")
                    # 使用新实现的去背景功能
                    comprehensive_processor.remove_background(input_path, output_path)

                elif action == 'convert_to_word':
                    output_filename = f"{base_name}.docx"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"转换为Word: {input_path} -> {output_path}")
                    # 使用新的转换函数，避免乱码问题
                    comprehensive_processor.convert_to_word(input_path, output_path)

                elif action == 'remove_stamp':
                    output_filename = f"去公章-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"去除公章: {input_path} -> {output_path}")
                    comprehensive_processor.process_pdf_comprehensive(input_path, output_path, "remove_stamp")

                elif action == 'remove_both':
                    output_filename = f"去红头公章-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"去除红头和公章: {input_path} -> {output_path}")
                    comprehensive_processor.process_pdf_comprehensive(input_path, output_path, "remove_both")

                elif action == 'remove_sensitive':
                    output_filename = f"去敏感信息-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"去除敏感信息: {input_path} -> {output_path}")
                    # 简单复制文件，因为我们没有实现这个功能
                    import shutil
                    shutil.copy(input_path, output_path)

                elif action == 'remove_all':
                    output_filename = f"全部去除-{original_name}"
                    output_path = os.path.join(app.config['PROCESSED_FOLDER'], safe_filename(output_filename))
                    logging.info(f"全部去除: {input_path} -> {output_path}")
                    # 简单复制文件，因为我们没有实现这个功能
                    import shutil
                    shutil.copy(input_path, output_path)

                # Check if output file was created
                if not os.path.exists(output_path):
                    logging.error(f"Output file was not created: {output_path}")
                    return jsonify({'error': f'处理失败，未能生成输出文件: {output_filename}'}), 500

                processed_files.append({
                    'original_name': original_name,
                    'processed_name': output_filename,
                    'path': output_path
                })

            except Exception as e:
                logging.error(f"Error processing file {file_info.get('original_name', 'unknown')}: {str(e)}")
                logging.error(traceback.format_exc())
                return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

    except Exception as e:
        logging.error(f"Error in process_files: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify({'error': f'处理失败: {str(e)}'}), 500

    # If only one file, return it directly
    if len(processed_files) == 1:
        return jsonify({
            'success': True,
            'single_file': True,
            'file_path': processed_files[0]['path'],
            'file_name': processed_files[0]['processed_name']
        })

    # If multiple files, create a zip archive
    zip_filename = 'processed_files.zip'
    zip_path = os.path.join(app.config['PROCESSED_FOLDER'], zip_filename)


    with zipfile.ZipFile(zip_path, 'w') as zipf:
        for file_info in processed_files:
            zipf.write(file_info['path'], arcname=file_info['processed_name'])

    return jsonify({
        'success': True,
        'single_file': False,
        'file_path': zip_path,
        'file_name': zip_filename
    })

@app.route('/download/<path:filename>')
def download_file(filename):
    return send_file(filename, as_attachment=True)

@app.route('/network-info')
def network_info():
    """返回网络信息，包括局域网IP地址"""
    local_ip = get_local_ip()
    return jsonify({
        'local_ip': local_ip,
        'port': 5001
    })

@app.route('/cleanup', methods=['POST'])
def manual_cleanup():
    """手动触发清理操作"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        max_age_hours = data.get('max_age_hours')

        # 如果提供了max_age_hours参数，将其转换为浮点数
        if max_age_hours is not None:
            try:
                max_age_hours = float(max_age_hours)
            except ValueError:
                return jsonify({'success': False, 'message': '无效的max_age_hours参数'}), 400

        # 执行清理
        cleanup_directories(max_age_hours)

        return jsonify({
            'success': True,
            'message': '清理操作已完成',
            'max_age_hours': max_age_hours
        })
    except Exception as e:
        logging.error(f"手动清理时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'清理操作失败: {str(e)}'}), 500

def get_local_ip():
    """获取本机局域网IP地址"""
    import socket
    try:
        # 创建一个临时socket连接到外部，获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        logging.error(f"获取本机IP地址失败: {str(e)}")
        return "127.0.0.1"  # 如果获取失败，返回本地回环地址

if __name__ == '__main__':
    # 获取本机局域网IP地址
    local_ip = get_local_ip()

    # 打印访问信息
    print("\n" + "="*60)
    print(f"本地访问地址: http://127.0.0.1:5001")
    print(f"局域网访问地址: http://{local_ip}:5001")
    print("="*60 + "\n")

    # 启动应用，监听所有网络接口，关闭调试模式提高性能
    app.run(host='0.0.0.0', port=5001, debug=False, threaded=True)

@echo off
echo Installing PDF Editor using pre-compiled wheel packages
echo This script will download and install all dependencies without compilation

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed! Please install Python 3.8 or higher.
    echo You can download Python from https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Create virtual environment directly with venv module
echo Creating virtual environment...
if not exist venv (
    python -m venv venv
) else (
    echo Virtual environment already exists, skipping creation.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Upgrade pip to latest version
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install wheel package first
echo Installing wheel package...
python -m pip install wheel

REM Create wheels directory if it doesn't exist
if not exist wheels mkdir wheels

echo Downloading and installing pre-compiled packages...

REM Install Flask and dependencies
echo Installing Flask and dependencies...
python -m pip install Flask==2.0.1
python -m pip install flask-cors==3.0.10
python -m pip install Werkzeug==2.0.1
python -m pip install Jinja2==3.0.1
python -m pip install itsdangerous==2.0.1
python -m pip install click==8.0.1
python -m pip install Six==1.16.0

REM Install NumPy (pre-compiled)
echo Installing NumPy...
python -m pip install numpy==1.19.5

REM Install Pillow (pre-compiled)
echo Installing Pillow...
python -m pip install Pillow==8.3.2

REM Install PyMuPDF (use an older version with wheels available)
echo Installing PyMuPDF...
python -m pip install PyMuPDF==1.18.17

REM Install OpenCV (headless version)
echo Installing OpenCV...
python -m pip install opencv-python-headless==********

REM Install pdf2image
echo Installing pdf2image...
python -m pip install pdf2image==1.16.0

REM Install python-docx
echo Installing python-docx...
python -m pip install python-docx==0.8.11

REM Install pdf2docx
echo Installing pdf2docx...
python -m pip install pdf2docx==0.5.6

REM Create necessary directories
echo Creating necessary directories...
if not exist uploads mkdir uploads
if not exist processed mkdir processed

REM Download Poppler manually
echo.
echo =====================================================================
echo Please download and install Poppler manually:
echo 1. Download Poppler from: 
echo    https://github.com/oschwartz10612/poppler-windows/releases/download/v23.08.0-0/Release-23.08.0-0.zip
echo 2. Extract the downloaded file
echo 3. Rename the extracted folder to "poppler"
echo 4. Place the "poppler" folder in the current directory
echo =====================================================================
echo.

set /p answer=Have you completed the Poppler installation? (y/n): 
if /i "%answer%" neq "y" (
    echo Please complete the Poppler installation before running the application.
    pause
    exit /b 1
)

REM Check if Poppler exists
if not exist poppler\Library\bin (
    echo Poppler installation not detected correctly. Please ensure:
    echo 1. You have downloaded and extracted Poppler
    echo 2. The extracted folder is renamed to "poppler"
    echo 3. The "poppler" folder is placed in the current directory
    echo 4. The folder structure is correct (should include Library\bin subdirectory)
    pause
    exit /b 1
)

echo.
echo Installation complete!
echo Use run_ascii.bat to start the application.
echo.
pause
